<template>
	<view class="example">
		<view class="title">跨行</view>
		<v-table :columns="columns" :list="dataRowSpan" :span-method="arraySpanMethod"></v-table>
	</view>
</template>

<script>
	import vTable from "@/components/no-bad-table/table.vue"
	export default{
		components: {
			vTable
		},
		data(){
			return {
				columns: [{
						title: "ID",
						key: "id"
					},
					{
						title: 'Name',
						key: 'name'
					},
					{
						title: 'Age',
						key: 'age'
					},
					{
						title: 'Address',
						key: 'address'
					}
				],
				//合并行
				dataRowSpan: [{
						name: '<PERSON>',
						age: 18,
						address: 'New York No. 1 Lake Park',
						id: "1",
						rowspan: 3
					},
					{
						name: '<PERSON>',
						age: 25,
						address: 'London No. 1 Lake Park',
						id: "2",
						rowspan: 0
					},
					{
						name: '<PERSON>',
						age: 30,
						address: 'Sydney No. 1 Lake Park',
						id: "3",
						rowspan: 0
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "4",
						rowspan: 1
					}
				],
			}
			
		},
		methods:{
			arraySpanMethod(
				row,
				column,
				rowIndex,
				columnIndex
			) {
				if (columnIndex == 0) {
			
					if (row.rowspan) {
						return {
							rowspan: row.rowspan,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 1
						};
					}
				}
				else{
					return {
						rowspan: 1,
						colspan: 1
					};
				}
			},
		}
		
	}
</script>

<style>
</style>
