<template>
	<view>
		<js-lang :title="title"></js-lang>
		<js-error mode="bottom" ref="jsError"></js-error>
		<u-sticky class="u-sticky">
			<!-- bg-blue  -->
			<view class="padding-sm flex light " style="background-color: #eee;" @tap="show=true">
				<view style="width: 100%"><u-search placeholder="商品/公司/形象刊等" v-model="keyword" bg-color="#fff"
						:show-action="false" :disabled="true" @tap="show=true" searchIconSize="26"
						:inputStyle="inputStyle"></u-search>
				</view>
				<!-- <u-icon @click="show=true" :name="filterIcon" size="60"
					class="margin-sm-left flex-sub margin-sm-right"></u-icon> -->

			</view>
		</u-sticky>
		<u-popup v-model="show" mode="right" length="90%">
			<!-- class="form"  -->
			<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">常用信息</view>
					<u-form-item label="公司:" prop="companyName" label-width="230">
						<js-select v-model="query.companyCode" :showFilter="false" :items="companySelectList"  placeholder="请选择" :tree="true"
							:label-value="query['company.companyName']" @label-input="query['company.companyName'] = $event"></js-select>
					</u-form-item>
				
					<u-form-item  label="商品名称:" prop="invName" label-width="230">
						<u-input placeholder="请输入" v-model="query['basInv.invName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="形象刊:" prop="cfree1" label-width="230">
						<u-input placeholder="请输入" v-model="query['freeVO.cfree1']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="仓库:" prop="djNo" label-width="230">
						<u-input placeholder="请输入" v-model="query['parent.basWare.cwhname']" type="text" maxlength="200"></u-input>
					</u-form-item>
					
					<!-- <u-form-item label="上架时间(起)" prop="time" label-width="230">
						<u-input placeholder="请输入" v-model="query.arrDate_gte" type="select" :select-open="startTime"
							@click="startTime = true"></u-input>
					</u-form-item>
					<u-form-item label="上架时间(止)" prop="time" label-width="230">
						<u-input placeholder="请输入" v-model="query.arrDate_lte" type="select" :select-open="endTime" @click="endTime = true"></u-input>
					</u-form-item> -->
					<u-picker mode="time" v-model="startTime" @confirm="startConfirm"></u-picker>
					<u-picker mode="time" v-model="endTime" @confirm="endConfirm"></u-picker>
				</view>
				<!-- <view style="" class="text-border">
					<view class="text text-lg text-green">到货信息</view>
					<u-form-item label="存货编码:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.basInv.cinvcode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="存货名称:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.basInv.cinvname']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="规格:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.basInv.cinvspec']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="型号:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.basInv.cinvtype']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="订单号:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.orderCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="订单行号:" prop="cusName" label-width="230">
						<u-input placeholder="请输入" v-model="query['queryAsnC.crowno']" type="text" maxlength="200"></u-input>
					</u-form-item>
				</view>
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">其它信息</view>
					<u-form-item label="公司:" prop="companyName" label-width="230">
						<js-select v-model="query.pkCorp" :showFilter="false" :items="companySelectList"  placeholder="请选择" :tree="true"
							:label-value="query['company.companyName']" @label-input="query['company.companyName'] = $event"></js-select>
					</u-form-item>
					<u-form-item label="仓库:" prop="wareName" label-width="230">
						<u-input placeholder="请输入" v-model="query['wareName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="采购员:" prop="cgName" label-width="230">
						<u-input placeholder="请输入" v-model="query['cgName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="采购部门:" prop="cgDeptName" label-width="230">
						<u-input placeholder="请输入" v-model="query['cgDeptName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item label="制单人:" prop="createByName" label-width="230">
						<u-input placeholder="请输入" v-model="query.createByName" type="text" maxlength="200"></u-input>
					</u-form-item>
					
				</view> -->
				
				
			</u-form>
			<view class="cu-bar"></view>
			<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
				<!-- round -->
				<button class="cu-btn  lines-red lg " @click="reset">重置</button>
				<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
			</view>
		</u-popup>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        " :style="{ height: computedScrollViewHeight  }">
				<view v-for="(item,index) in list"  class="cu-item shadow " style="position: relative;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<view class="text-red text-bold"> {{ item.parent.code|| ""  }}</view>
						<!-- <view class="">
							<dictLabel style="margin-left: 10px;" :value="item.djStatus" dict-type="xy_asn_dj_status">
							</dictLabel>
						</view> -->
					</view>
					
					<view class="cu-form-group">
						<view class="title">公司：</view>
						<view style="flex: 1;"> {{ item.parent.company.companyName|| ""  }}
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title">仓库：</view>
						<view style="flex: 1;"> {{ item.parent.basWare.cwhname|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">业务类型：</view>
						<view style="flex: 1;"> 
						<js-select v-model="item.parent.busType" dict-type="wh_poJust_bus_type"
								placeholder=" " :disabled="true"></js-select>
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title">商品编码：</view>
						<view style="flex: 1;">{{ item.basInv.viewCode || ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">商品名称：</view>
						<view style="flex: 1;">{{ item.basInv.invName || ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">形象刊：</view>
						<view style="flex: 1;"> {{ item.freeVO?item.freeVO.cfree1: ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">数量：</view>
						<view style="flex: 1;"> {{ item.iqty|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">调整前货位：</view>
						<view style="flex: 1;"> {{ item.beforePosition.posName|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">调整后货位：</view>
						<view style="flex: 1;"> {{ item.afterPosition.posName|| ""  }} </view>
					</view>
					
					<view class="cu-form-group">
						<view class="title">来源单据：</view>
						<view style="flex: 1;"> {{ item.sourceId|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">操作时间：</view>
						<view style="flex: 1;"> {{ item.parent.createDate|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">操作人：</view>
						<view style="flex: 1;"> {{ item.parent.createByName|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">备注：</view>
						<view style="flex: 1;"> {{ item.parent.remarks|| ""  }} </view>
					</view>
					<!-- <view
					  class="cu-form-group"
					  style="color: #3e97b0"
					>
						<view class="">
						</view>
						<view @click="toForm(item)">
							查看详情<u-icon name="arrow-right"></u-icon>
						</view>
					</view> -->
					
				</view>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>
		<u-modal v-model="smshow" title="ASN单" @confirm="confirm" :show-cancel-button="true" width="80%">
			<view class="slot-content">
				<view class="cu-bar" style="padding: 10px">
					<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请扫描ASN单号"
						:show-action="false" @search="confirm"></u-search>
					<view style="margin-left: 10px; display: flex; flex-direction: column">
						<u-icon @click="search" name="scan" size="50"></u-icon>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				title:'记录',
				companySelectList:[],
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				show: false,
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
					// orderBy: "a.create_date desc",
				},
				loadStatus: "loadmore",
				triggered: false,
				// flag: hasPermission('app:proj:weekly:pmWeekly:edit'),
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				projStatus: this.$store.state.auth.projStatus,
			};
		},
		onShow() {
			this.$u.api.ktnw.companyTreeData().then(res => {
				this.companySelectList = res;
			});
		},
		onLoad(e) {
			this.title= e.busType == '1'?'其它上架':e.busType == '2'?'其它下架'
			:e.busType == '3,4'?'拣货记录':''
			this.query.pageNo = 1;
			// this.query['parent.busType'] = e.busType;
			this.query['busTypes'] = e.busType;
			this.loadData();

		},
		mounted() {
			// this.$refs.xmInfo.$on('child-mounted-done', () => {
			//   this.calculateScrollViewHeight();
			// });
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			confirm() {
				let _this = this;
				if (this.barCode) {
					this.$u.api.xy
						.needShListData({
							pageNo: 1,
							pageSize: 20,
							djno:this.barCode.substring(4)
						})
						.then((res) => {
							console.log(res, "res");
							if (res.list.length == 1) {
								uni.navigateTo({
									url: '/pages/xy/asn/form?djno=' + res.list[0].djno,
								})
								_this.smshow = false;
							} else {
								_this.focus = false;
								// this.$u.toast("请扫描正确的任务单号");
								_this.$refs.jsError.showError("", "请扫描正确的ASN单号", "warn");
							}
							this.barCode = "";
							setTimeout(() => {
								_this.focus = true;
							}, 500);
						});
				} else {
					// this.$u.toast("任务单号不能为空!");
					_this.$refs.jsError.showError("", "ASN单号不能为空", "warn");
				}
			},
			search() {
				let _that = this;
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			carNoconfirm(v) {
				this.query.carNo = v;
				this.$forceUpdate()
			},
			showKeyboard(ref) {
				this.$refs[ref].toShow(this.query.carNo)
			},
			handlePos(item) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/invPosList?id=' + item.id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			toForm(item){
				const that = this
				uni.navigateTo({
					url: '/pages/xy/asnH/form?djno=' + item.djno,
				})
			},
			editDetail(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhEdit?id=' + model.id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			addFH(item) {
				const that = this
				let id = item.id ? item.id : ''
				uni.navigateTo({
					url: '/pages/mf/fh/fhdj?id=' + id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			//跳转详情页面上传图片
			toFj(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhfj?model=' + JSON.stringify(model),
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			czsubmit(model) {
				this.show2 = false
				let data = {
					djNo: this.czData.djNo,
					cstatus: 2,
					id: this.czData.id,
					remarks: this.czData.remarks
				}
				this.$u.api.mffh.updateCzStatusByHand(data).then(res => {
					if (res.result == 'true') {
						this.$u.toast(res.message);
						this.loadData();
					} else {
						this.$refs.jsError.showError('', res.message, 'error');
					}
				})
			},
			btnOver(model) {
				this.show2 = true
				this.czData = model
			},
			updateCstatus(model) {
				const that = this;
				uni.showModal({
					title: '修改提示',
					content: '确认二次称重吗？',
					confirmColor: '#00007f',
					success: (res) => {
						if (!res.confirm) {
							return false;
						} else {
							this.$u.api.mffh.updateCstatus({
								id: model.id,
								cstatus: 1
							}).then(res => {
								if (res.result == 'true') {
									that.$u.toast(res.message);
									that.loadData();
								} else {
									that.$refs.jsError.showError('', res.message, 'error');
								}
							})
						}
					}
				})
			},
			btnSdPush(model) {
				const that = this;
				uni.showModal({
					title: '修改提示',
					content: '确认修改为推送状态吗？',
					confirmColor: '#00007f',
					success: (res) => {
						if (!res.confirm) {
							return false;
						} else {
							this.$u.api.mffh.updatePushStatusByHand({
								id: model.id
							}).then(res => {
								if (res.result == 'true') {
									that.$u.toast(res.message);
									that.loadData();
								} else {
									that.$refs.jsError.showError('', res.message, 'error');
								}
							})
						}
					}
				})
			},
			erpPush(model) {
				const that = this;
				uni.showModal({
					title: '修改提示',
					content: '确认推送ERP吗？',
					confirmColor: '#00007f',
					success: (res) => {
						if (!res.confirm) {
							return false;
						} else {
							this.$u.api.mffh.updatePushStatusByHand({
								id: model.id
							}).then(res => {
								if (res.result == 'true') {
									that.$u.toast(res.message);
									that.loadData();
								} else {
									that.$refs.jsError.showError('', res.message, 'error');
								}
							})
						}
					}
				})
			},
			toEdit(model) {
				const that = this;
				uni.showModal({
					title: '修改提示',
					content: '是否准备就绪？',
					confirmColor: '#00007f',
					success: (res) => {
						if (!res.confirm) {
							return false;
						} else {
							this.$u.api.mffh.invalid({
								id: model.id
							}).then(res => {
								if (res.result == 'true') {
									that.$u.toast("成功！");
									that.loadData();
								} else {
									that.$refs.jsError.showError('', res.message, 'error');
								}
							})
						}
					}
				})
			},
			toAdd(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhList?hid=' + model.id + '&carType=' + model.carType,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			delData(id) {
				// this.query.id = id;
				uni.showModal({
					title: '删除提示',
					content: '是否删除此计划？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							this.isdisabled = true
							return false;
						}
						this.$u.api.mffh.delete({
							id
						}).then(res => {
							if (res.result == "true") {
								this.$u.toast("删除成功！");
							} else {
								this.$refs.jsError.showError('', res.message, 'error');
							}
							// this.query.id = ''
							// this.list = [];
							this.query.pageNo = 1;
							this.loadData();
						})
					}
				})
			},
			delDetail(id) {
				// this.query.id = id;
				uni.showModal({
					title: '删除提示',
					content: '是否删除此详情？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							this.isdisabled = true
							return false;
						}
						this.$u.api.mffh.delDetail({
							id
						}).then(res => {
							if (res.result == "true") {
								this.$u.toast("删除成功！");
							} else {
								this.$refs.jsError.showError('', res.message, 'error');
							}
							// this.query.id = ''
							// this.list = [];
							this.query.pageNo = 1;
							this.loadData();
						})
					}
				})
			},
			// this.$forceUpdate();
			reset() {
				this.list = [];
				this.query = {
					busTypes:this.query.busTypes,
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			submit() {
				this.list = [];
				this.query.pageNo = 1;
				this.loadData();
				this.show = false
			},
			startConfirm(e) {
				this.query.arrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.arrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				// uni.navigateTo({
				//   url: "/pages/zfgs/index/index/index?item=" + JSON.stringify(this.itemContent),
				// });
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// await new Promise((resolve) => {
					//   this.$nextTick(() => {
					//     this.headerHeight = this.$refs.xmInfo.$refs['u-sticky'].height + this.$refs.xmInfo.$refs['u-sticky'].h5NavHeight + this.$refs.navbar.navbarHeight;
					//     resolve();
					//   });
					// });
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.ktnw.poJustListData(this.query).then((res) => {
					if (res.list.length >= 0) {
						// this.xmList = res.list
						if (res.list.length < 20 || res.list.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res.list;
						if (type == "add") {
							for (var i = 0; i < data.length; i++) {
								this.list.push(data[i]);
							}
						} else {
							this.list = data;
						}
					}
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 260rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>