<template>
	<view>
		<u-popup v-model="bshow" :mode="mode" z-index="99999" @close="handleClose">
			 <view class="u-popup-slot" :style="">
			 	<u-collapse class="box u-p-b-5" :accordion="false" :arrow="true">
			 		<view class="item">
			 			<u-collapse-item :open="true">
			 				<view class="title" slot="title">
			 					<u-icon :name="icon" :size="40" :style="style"></u-icon>
			 					<view class="text" :style="style">{{title}}</view>
			 				</view>
			 			</u-collapse-item>
			 			<view class="padding-left-lg">
							<view v-html="msg">
							</view>
			 				<!-- {{msg}} -->
			 			</view>
			 		</view>
					
			 	</u-collapse>
			 </view>
		</u-popup>
	</view>
</template>

<script>
	/**
	 * 错误消息提示组件
	 * @property {String} msg 错误消息
	 * @property {Boolean} bshow 是否显示
	 * @property {String} icon 图标 error=错误 warn=警告 info=消息
	 * @property {String} mode 弹出方向，left|right|top|bottom|center
	 * @property {String} title 消息标题
	 * @property {String} style 样式
	 * @example <js-error mode="bottom" ref="jsError"></js-error>
	 * 显示：this.$refs.jsError.showError('异常提示xxx','测试xxx','info');
	 * <AUTHOR>
	 * @version 2022-7-11
	 */
	export default {
		name:"js-error",
		props: {
			mode: {
				type: String,
				default: 'bottom'
			},
		},
		created() {
			//this.loadData();
		},
		data() {
			return {
				msg: '',
				bshow: false,
				icon: 'error',
				title: '消息提示',
				style: 'color:red;'
			};
		},
		methods: {
			showError(title,msg,type){
				if(title){
					this.title=title;
				}
				// msg = msg.replace(/posfull:[^,\n]*/g, '');
				msg = msg.replace(/posfull:/g, '');
				if(type=='error'){
					this.icon='error';
					this.style='color:red'
				}else if(type=='warn'){
					this.icon='warning';
					this.style='color:gold'
				}else {
					this.icon='info';
					this.style='color:green'
				}
				this.msg=msg;
				this.bshow=true;
			},
			handleClose() {
			// this.$emit('close', false);
			// 传递给父组件
				this.bshow=false;
				this.$emit("update:errorClose", false);
			},
		}
	}
</script>

<style lang="scss">
.text{
	font-size: 38rpx !important;
}
.title{
	width: 260rpx;
	justify-content:flex-start !important;
}
.padding-left-lg{
	min-height: 200rpx;
	vertical-align: middle;
	font-size: 32rpx;
}
</style>