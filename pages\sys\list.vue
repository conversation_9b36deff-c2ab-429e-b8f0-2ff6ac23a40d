<template>
	<view class="wrap ">
		<view style="background-color: #fff;margin-bottom: 20rpx;">
			<view style="padding: 10px;">
				<view style=" text-align: center; ">
					<u-form class="form bg-white" style="background-color: transparent;" :model="model" ref="uForm"
						label-position="left">
						
						<js-select v-model="model.companyCode" :showFilter="false" :items="selectList"  placeholder="请选择公司" :tree="true"
							:label-value="model['companyName']" @label-input="model['companyName'] = $event"  @confirm="selectConfirm"></js-select>
					</u-form>
				</view>
			</view>
		</view>
		
		
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->
		<view v-for="res in menuList1" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.id" @click="navTo(item.url)" style="margin-bottom: 15rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view>
			
			<!-- <view class="margin-sm ">
				<view class="flex bg-white padding radius justify-between" v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 20rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view> -->
		</view>
		

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	import checkVersion from "@/pages/lq-upgrade/checkVersion.js";
	export default {
		data() {
			return {
				model:{},
				selectList:[],
				stringPermissions: [],
				imgList: [
					{image: '/static/jeesite/banner/1.png'},
					{image: '/static/jeesite/banner/3.png'},
					{image: '/static/jeesite/banner/2.jpg'},
					{image: '/static/jeesite/banner/4.png'},
				],
				todoCount: 0,
				menuList1: [],
				menuList: [
					{
						extend:{
							extendS2:'menue:ktnw:dpd'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/pd/list?type=0',
					},
					{
						extend:{
							extendS2:'menue:ktnw:ypd'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/pd/list?type=1',
					},
					{
						extend:{
							extendS2:'menue:ktnw:dpdpos'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/pdPos/list?type=0',
					},
					{
						extend:{
							extendS2:'menue:ktnw:ypdpos'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/pdPos/list?type=1',
					},
					{
						extend:{
							extendS2:'menue:ktnw:djh'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/jh/list?type=0',
					},
					{
						extend:{
							extendS2:'menue:ktnw:jhjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=3,4',
					},
					{
						extend:{
							extendS2:'menue:ktnw:yjh'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/jh/list?type=1',
					},
					{
						extend:{
							extendS2:'menue:ktnw:ckdd'
						},
						// menuIcon: '/static/image/zfgs/index/jhjl.png',
						menuIcon: '/static/image/zfgs/index/ckdd.png',
						url: '/pages/ktnw/ckdd/list',
					},
					// 其它上架
					// {
					// 	extend:{
					// 		extendS2:'menue:ktnw:qtsj'
					// 	},
					// 	menuIcon: '/static/image/zfgs/index/sj.png',
					// 	url: '/pages/ktnw/qtsj/index',
					// },
					{
						extend:{
							extendS2:'menue:ktnw:sjjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=1',
					},
					// 其它下架
					// {
					// 	extend:{
					// 		extendS2:'menue:ktnw:qtxj'
					// 	},
					// 	menuIcon: '/static/image/zfgs/index/xj.png',
					// 	url: '/pages/ktnw/qtxj/index',
					// },
					{
						extend:{
							extendS2:'menue:ktnw:xjjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=2',
					},
					// 货位调整
					{
						extend:{
							extendS2:'menue:ktnw:hwtz'
						},
						menuIcon: '/static/image/zfgs/index/hwtz.png',
						url: '/pages/ktnw/hwtz/index',
					},
					// 销售退货
					{
						extend:{
							extendS2:'menue:ktnw:xsth'
						},
						menuIcon: '/static/image/zfgs/index/xsth.png',
						url: '/pages/ktnw/xsth/list',
					},
					// 入库上架
					{
						extend:{
							extendS2:'menue:ktnw:rksj'
						},
						menuIcon: '/static/image/zfgs/index/sj.png',
						url: '/pages/ktnw/rksj/list2',
					},
					// 其他入库
					{
						extend:{
							extendS2:'menue:ktnw:qtrksj'
						},
						menuIcon: '/static/image/zfgs/index/sj.png',
						url: '/pages/ktnw/qtrksj/list2',
					},
					{
						extend:{
							extendS2:'menue:ktnw:qtrkxj'
						},
						menuIcon: '/static/image/zfgs/index/xj.png',
						url: '/pages/ktnw/qtrkxj/list?type=0',
					},
					{
						extend:{
							extendS2:'menue:ktnw:qtrkxjy'
						},
						menuIcon: '/static/image/zfgs/index/xj.png',
						url: '/pages/ktnw/qtrkxj/list?type=1',
					},
					// 
					// 退料下架
					{
						extend:{
							extendS2:'menue:ktnw:tlxj'
						},
						menuIcon: '/static/image/zfgs/index/xj.png',
						url: '/pages/ktnw/tlxj/list2',
					},
					// 商品重量
					{
						extend:{
							extendS2:'menue:ktnw:spzl'
						},
						menuIcon: '/static/image/zfgs/index/ckdd.png',
						url: '/pages/ktnw/spzl/index',
					},
					
				],
			}
		},
		computed: {
			...mapState(['vuex_company']),
		},
		onShow() {
			this.$u.api.ktnw.companyTreeData().then(res => {
				this.selectList = res;
			});
			this.$u.api.ktnw.getCorpCache().then(res => {
				this.model = res.data
				this.$u.vuex('vuex_company', this.model);
			});
		},
		created() {
			 
		},
		onLoad() {
			this.upgrade();
			var _this = this;
			this.$u.api.menuTree().then(res => {
				if (res.length > 0) {
					res.forEach(item => {
						if ('系统管理' == item.menuName) {
							item.childList.forEach(item2 => {
								if ('移动端管理' == item2.menuName) {
									// this.showMenu(item2.childList);
									item2.childList.forEach(item3 => {
										if ('APP菜单' == item3.menuName) {
											this.showMenu(item3.childList);
										}
									})
								}
							})

						}
					})

				}
			});
			// this.$u.api.authInfo().then(res => {
			// 	this.stringPermissions=(res==null || res.stringPermissions==null)?[]:res.stringPermissions;
			// });
		},
		methods: {
			//检查版本更新
			upgrade() {
			  // #ifdef APP-PLUS
			  this.$u.api.upgradeCheck().then((res) => {
			    let url = res.data.apkUrl
			      ? res.data.apkUrl
			      : this.vuex_config.xtUrl + res.data.xtUrl;
			    if (res.result == "true") {
			      checkVersion({
			        name: res.data.upTitle, //最新版本名称
			        code: res.data.upVersion, //最新版本号
			        content: `${res.data.upContent}`, //更新内容
			        url, //下载链接
			        // forceUpdate: true, //是否强制升级
			        forceUpdate: res.data.upType == '3'?true:false, //是否强制升级
			      });
			    }
			  });
			  // #endif
			},
			//显示菜单
			showMenu(list) {
				this.menuList1 = list
				this.menuList.forEach(item => {
					this.menuList1.forEach(res => {
						res.childList.forEach(req => {
							if (req.extend?.extendS2 == item.extend?.extendS2 ) {
								req.menuIcon = item.menuIcon;
								req.url = item.url;
							}
						})

					})
				})
			},
			async selectConfirm() {
				await this.$u.api.ktnw.switchCorp({
						companyCode: this.model.companyCode
					})
					.then(res => {
						this.$u.vuex('vuex_company', this.model);
					});
			},
			navTo(url) {
				console.log('url',url)
				uni.navigateTo({
					url: url
				});
			},
			imgListClick(index) {
				console.log(`点击了第${index + 1}页图片`)
			},
			itemClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style scoped lang="scss">
	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}
</style>