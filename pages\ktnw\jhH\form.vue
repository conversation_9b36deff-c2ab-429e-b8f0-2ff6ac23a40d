<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <u-form class="form bg-white" :model="model" ref="uForm" label-position="left">
			<u-form-item label="ASN单:" prop="djno" label-width="200"  >
				<u-input placeholder=" " v-model="model.djno" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="公司:" prop="companyName" label-width="200"  >
				<u-input placeholder=" " v-model="model.company.companyName" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="供应商:" prop="cusname" label-width="200"  >
				<u-input placeholder=" " v-model="model.basVen.cusname" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="仓库:" prop="wareName" label-width="200"  >
				<u-input placeholder=" " v-model="model.wareName" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="送货备注:" prop="remarks" label-width="200" >
				<u-input placeholder=" " v-model="model.remarks" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="收货反馈:" prop="remarks" label-width="200" >
				<u-input placeholder=" " v-model="model.arrRemarks" :disabled="true" type="text"  di maxlength="64"></u-input>
			</u-form-item>
		</u-form> -->
		<view class="cu-form-group ">
			<view class="title ">ASN单号：</view>
			<view  style="flex: 1;"> {{ model.djno|| ""  }}</view>
		</view>
		<view class="cu-form-group">
			<view class="title">公司：</view>
			<view style="flex: 1;"> {{ model.company.companyName|| ""  }}
			</view>
			
		</view>
		<view class="cu-form-group">
			<view class="title">供应商：</view>
			<view style="flex: 1;"> {{ model.basVen.cusname|| ""  }} </view>
		</view>
		<view class="cu-form-group">
			<view class="title">收货信息：</view>
			<view style="flex: 1;">{{ model.arrUserName || ""  }} （{{ model.arrDate || ""  }}） </view>
		</view>
		
		<view class="cu-form-group">
			<view class="title">仓库：</view>
			<view style="flex: 1;"> {{ model.wareName || ''}} </view>
		</view>
		<view class="cu-form-group">
			<view class="title">采购员：</view>
			<view style="flex: 1;"> {{ model.cgName|| ""  }} （{{ model.cgDeptName|| ""  }}）</view>
		</view>
		<view class="cu-form-group">
			<view class="title">送货备注：</view>
			<view style="flex: 1;"> {{ model.remarks|| ""  }} </view>
		</view>
		<view class="cu-form-group">
			<view class="title">收货反馈：</view>
			<view style="flex: 1;"> {{ model.arrRemarks|| ""  }} </view>
		</view>
		
		<view class="action bg-white "
			style="color: #3E97B0;font-size: 20px;align-items: center;display: flex;padding: 0 0 10px 10px;">
			<!-- <u-icon name="/static/image/detail.png" size="80"></u-icon> -->
			<text class="cuIcon-titles text-bold">收货单明细</text>
		</view>
		<view style="padding: 10px;">
			<view v-for="(item,index) in children" class="cu-item shadow " style="position: relative;" :key="index">
				<view class="cu-form-group " style="display: flex;">
					<!-- justify-content: space-between; -->
					<view
					 
						style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
						{{ index + 1 }}
					</view>
					<view style="width: 150rpx;">
					</view>
					<view style="flex: 1;">
						<!-- 	<text style="font-size: 35px;" class="cuIcon-deletefill text-sl text-red"
								@tap="delDetail(item.id)"></text> -->
								{{ item.orderCode|| ""  }} （{{ item.crowno|| ""  }}）
					</view>
				</view>
				<!-- <view class="cu-form-group text-bold text-red">
					<view class="title">订单号：</view>
					<view style="flex: 1;"> {{ item.orderCode|| ""  }}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">订单行号：</view>
					<view style="flex: 1;"> {{ item.crowno|| ""  }}
					</view>
				</view> -->
				<view class="cu-form-group">
					<view class="title">存货编码：</view>
					<view style="flex: 1;"> {{ item.basInv.cinvcode|| ""  }} </view>
				</view>
				<view class="cu-form-group">
					<view class="title">存货名称：</view>
					<view style="flex: 1;"> {{ item.basInv.cinvname|| ""  }} </view>
				</view>
				<view class="cu-form-group">
					<view class="title">规格：</view>
					<view style="flex: 1;"> {{ item.basInv.cinvspec|| ""  }} </view>
				</view>
				<view class="cu-form-group">
					<view class="title">型号：</view>
					<view style="flex: 1;"> {{ item.basInv.cinvtype|| ""  }} </view>
				</view>
				<!-- <view class="cu-form-group">
					<view class="title">计量单位：</view>
					<view style="flex: 1;"> {{ item.basInv.measname|| ""  }} </view>
				</view> -->
				<!-- <view class="cu-form-group">
					<view class="title">送货数：</view>
					<view style="flex: 1;">  （{{ item.basInv.measname|| ""  }} ）</view>
				</view> -->
				<view class="cu-form-group">
					<view class="title">收货/送货：</view>
					<view  style="flex: 1;"> {{ item.arrQty|| "0"  }} / {{ item.iqty|| "0"  }} （{{ item.basInv.measname|| ""  }} ）</view>
				</view>
				<view class="cu-form-group">
					<view class="title">收货反馈：</view>
					<view style="flex: 1;"> {{ item.arrRemarks|| ""  }} </view>
				</view>
			</view>

	</view>
	<view style="height: 80rpx;"></view>
	<view class="footer" >
		<!-- <view style="padding: 20rpx 20rpx 10rpx;display: flex;align-items: center;justify-content: space-between;">
			<view style="display: flex;align-items: center;" >
				<view style="width: 160rpx;">送货总数：</view>
				<view style="flex: 1;" > {{ model.sumQty|| "0"  }}</view>
			</view>
		</view> -->
		<view style="padding:20rpx;display: flex;align-items: center;justify-content: space-between;">
			<view style="display: flex;align-items: center;" >
				<view style="width: 220rpx;">收货/送货总数：</view>
				<view style="flex: 1;" class=" text-xxl "> {{ model.sumArrQty|| "0"  }} / {{ model.sumQty|| "0"  }}</view>
			</view>
			<view class="">
				共  {{children.length}}  条
			</view>
		</view>
		<!-- <view style=" display: flex;justify-content: space-around;padding: 20rpx ;">
			<button class="cu-btn lines-default shadow-blur" @tap="zdsh()">
				整单收货
			</button>
			<button style="margin: 0 10px;" class="cu-btn lines-green shadow-blur" @tap="submit()">
				确认收货
			</button>
			
		</view> -->
	</view>
	</view>
</template>

<script>
	import config from '@/common/config.js';
	export default {
		data() {
			return {
				shQty:'',
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				model: {
					company:{},
					basVen:{}
				},
				//主表id
				query: {
					id: '',
				},
				//子表id
				querys: {
					id: '',
				},
				children: [],
				carVenSelectList: [],
				pickerTime: false, //控制日期显示
			}
		},
		onLoad(params) {
			if(params.djno){
				this.loadList({djno:params.djno});
			}
			// uni.$on('refreshData', () => {
			// 	this.loadList();
			// })
			// uni.$on('carVenData', (data) => {
			// 	console.log(data,'data====');
			// 	this.model.carVenCode = data.id 
			// 	this.model.carVenName = data.name
			// 	this.$forceUpdate()
			// })
		},
		onShow() {},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			zdsh(){
				this.children.forEach(item=>{
					item.arrQty = item.iqty
				})
				this.shQty = this.children.reduce((prev, next) => {
					 const currentQty = Number(next.arrQty) || 0;
					 return Number(prev) + currentQty;
				}, 0);
				this.$forceUpdate()
			},
			iqtyChange(e, item, index) {
				// e = e.match(/^\d*(\.?\d{0,4})/g)[0]
				// item.arrQty = Number(e).toFixed(config.scaleArrQty)
				if(e < 0){
					item.arrQty = -e
				}
				this.shQty = this.children.reduce((prev, next) => {
					 const currentQty = Number(next.arrQty) || 0;
					 return Number(prev) + currentQty;
				}, 0);
				// this.shQty = this.shQty.toFixed(config.scaleArrQty)
				this.$forceUpdate()
			},
			carVenXz(){
				let obj = {}
				if(this.model.carVenName){
					obj = {
						id:this.model.carVenCode,
						name:this.model.carVenName,
					}
				}
				const that = this;
				uni.navigateTo({
					url: '/components/treeList/list?item=' + JSON.stringify(obj),
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {
							console.log(data)
						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						//res.eventChannel.emit('initfilter', that.queryData)
					}
				})
			},
			paste(){
				let that = this
				uni.getClipboardData({
					success: (res) => {  // 获取成功回调
						that.model.carNo = res.data.trim().slice(0,8)
						that.$forceUpdate()
						if(!that.model.cdriver && !that.model.driverPhone){
							that.$u.api.mffh.getCarInfoByCarNo({carNo:that.model.carNo}).then(res => {
								that.model.cdriver = res.data.cdriver || '';
								that.model.driverPhone = res.data.driverPhone || '';
								that.$forceUpdate()
							});
						}
					}
				})
			},
			carNoconfirm(v){
				this.model.carNo = v;
				this.$forceUpdate()
				if(!this.model.cdriver && !this.model.driverPhone){
					this.$u.api.mffh.getCarInfoByCarNo({carNo:v}).then(res => {
						this.model.cdriver = res.data.cdriver || '';
						this.model.driverPhone = res.data.driverPhone || '';
						this.$forceUpdate()
					});
				}
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			cancel() {
				// uni.$emit('refreshData');
				uni.navigateBack({
					delta: 1,
				})
			},
			toFindDetail(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhDetailList?hid=' + model.id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			toAddDetail(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhHwAdd?hid=' + model.id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			delDetail(id) {
				this.querys.id = id;
				uni.showModal({
					title: '删除提示',
					content: '是否删除此详情？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							this.isdisabled = true
							return false;
						}
						this.$u.api.mffh.delDetail(this.querys).then(res => {
							if (res.result=="true") {
								this.$u.toast("删除成功！");
							} else {
								// this.$u.toast(res.message);
								this.$refs.jsError.showError('',res.message,'error');
							}
							this.querys.id = ''
							this.loadList();
						})
					}
				})
			},
			loadList(data) {
				this.$u.api.xy.asnHForm(data).then(res => {
					this.model = res.asnH;
					this.children = res.asnH.asnCList || [];
				})
			},
			GoaddChild() {
				if (this.model.id == null || this.model.id == '') {
					this.$refs.jsError.showError('','请先保存发货信息！','error');
					return;
				} else {
					const that = this;
					uni.navigateTo({
						url: '/pages/mf/fh/fhList?hid=' + this.model.id + '&carType='+ this.model.carType,
						events: {
							// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
							Filter(data) {
								console.log(data)
							}
						},
						success: function(res) {
							// 通过eventChannel向被打开页面传送数据
							//res.eventChannel.emit('initfilter', that.queryData)
						}
					})
				}
			},
			timeConfirm(e) {
				// this.model.planArrDate = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ":" + e.minute + ":" + e
				// 	.second;
				this.model.planArrDate = e.year + '-' + e.month + '-' + e.day;
			},
			submit() {
				// if (this.model.carNo == null || this.model.carNo == '') {
				// 	this.$refs.jsError.showError('','请正确填入车牌号！','error');
				// 	return;
				// } else if (this.model.planDate == null || this.model.planDate == '') {
				// 	this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
				// 	return;
				// } else if (this.model.carVenCode == null || this.model.carVenCode == '') {
				// 	this.$refs.jsError.showError('','请正确选择运输单位！','error');
				// 	return;
				// } else {
					this.model.asnCList = this.children
					this.$u.api.xy.asnHComfirmAsn(this.model).then(res => {
						if (res.result == 'true') {
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				// }
			},
		}
	}
</script>
<style lang="less">
	/* .footer {
	position: fixed;
	bottom: 0;
	right:0;
	width: 100%;
	line-height: var(--footer-height);
	background: #ffffff;
	color: #ffffff;
} */
	.btn-plus {
		position: fixed;
		bottom: 260rpx;
		right: 40rpx;
		z-index: 2;
		opacity: 0.6;
	}

	.btn-plus:hover {
		opacity: 1;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */

	}

	.cu-bar {
		min-height: 80px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 230rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}
	

	.cu-modal-footer {
		padding: 32rpx 32rpx !important;
		width: 100% ;
		.cu-btn {
			width: 50%;
		}
		
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
	
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;z-index: 999;
		border-top: 1px solid #eee;
	}
</style>