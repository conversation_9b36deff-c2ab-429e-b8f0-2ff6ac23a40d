		 <template>
		 	<view>
		 		<view class="content"></view>
		 	</view>
		 </template>
		 <!-- 激光扫码，广播模式 -->
		 <script>
		 	var main, receiver, filter;
		 	var codeQueryTag = false;
		 	export default {
		 		data() {
		 			return {
		 				scanCode: ''
		 			}
		 		},
		 		created() {
					// 初始化
		 			this.initScan()
					// 启动广播
		 			this.startScan();
		 		},
		 		onHide() {
					// 结束广播
		 			this.stopScan();
		 		},
		 		destroyed() {
					// 结束广播
		 			this.stopScan();
		 		},
		 		methods: {
					// 初始化
		 			initScan() {
		 				//  #ifdef APP
		 				// console.log('initScan:扫码初始化');
		 				let that = this;
		 				main = plus.android.runtimeMainActivity(); //获取activity
		 				//var context = plus.android.importClass('android.content.Context'); //上下文
		 				var IntentFilter = plus.android.importClass('android.content.IntentFilter');
		 				filter = new IntentFilter();
		 				//下面的addAction 改为自己 pad 设备的广播动作（在扫描设置或者厂商附带的app 里面设置为广播模式，然后查看相应参数）
		 				filter.addAction("android.intent.action.SCANRESULT");
		 				receiver = plus.android.implements('io.dcloud.feature.internal.reflect.BroadcastReceiver', {
		 					onReceive: (context, intent) => {
								
		 						console.log('---onReceive：', context, intent);
		 						plus.android.importClass(intent);
		 						//下面的getStringExtra内改为自己的广播标签（键值/key）： data
		 						console.log('---***getStringExtra： ', intent.getStringExtra("value"));
		 						// 海康
								// let code = intent.getStringExtra("ScanCode");
								// console.log('---扫码data： ', code);
								//斑马 TC20
								var banMaSacanInfo = intent.getStringExtra('value');	 // callback(intent.getStringExtra('com.motorolasolutions.emdk.datawedge.data_string'));
								console.log('扫描结果', banMaSacanInfo)
		 						
								// 传入接收到的参数
		 						that.queryCode(banMaSacanInfo);
		 					}
		 				});
		 				// #endif
		 			},
		 			// 开启广播
					startScan() {
		 				//  #ifdef APP
		 				console.log('startScan,开启广播接收');
		 				main.registerReceiver(receiver, filter);
		 				// #endif
		 			},
					// 关闭广播
		 			stopScan() {
		 				//  #ifdef APP
		 				console.log('stopScan，结束');
		 				main.unregisterReceiver(receiver);
		 				// #endif
		 			},
		 			// 避免重复扫码
		 			queryCode: function(code) {
		 				//  #ifdef APP
		 				if (codeQueryTag) return false;
		 				codeQueryTag = true;
		 				setTimeout(function() {
		 					codeQueryTag = false;
		 				}, 150);
		 				// console.log('-****--扫码code： ', code);
		 				let data = code
		 				uni.$emit('xwscan', {
		 					code: data
		 				})
		 				// #endif
		 			}
		 		}
		 	}
		 </script>