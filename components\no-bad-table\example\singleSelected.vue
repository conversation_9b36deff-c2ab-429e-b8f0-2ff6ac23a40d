<template>
	<view class="example">
		<view class="title">数据操作 单选</view>
		<v-table :columns="columnsCheckBox" :list="data" selection="single"  @on-selection-change="onSelectionChange"></v-table>
	</view>
</template>

<script>
	import vTable from "@/components/no-bad-table/table.vue"
	export default{
		components: {
			vTable
		},
		data(){
			return {
				columnsCheckBox: [{
						title: 'Name',
						key: 'name'
					},
					{
						title: 'Age',
						key: 'age'
					},
					{
						title: 'Address',
						key: 'address'
					}
				],
				data: [{
						name: '<PERSON>',
						age: 18,
						address: 'New York No. 1 Lake Park',
						id: "1",
						$checked:true
						
					},
					{
						name: '<PERSON>',
						age: 25,
						address: 'London No. 1 Lake Park',
						id: "2",
						$disabled:true
					},
					{
						name: '<PERSON>',
						age: 30,
						address: 'Sydney No. 1 Lake Park',
						id: "3"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "4"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "5"
					},
				
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "6"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "7"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "8"
					},
					{
						name: 'Jon Snow',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "9"
					}
				],
			}
		},
		methods:{
			onSelectionChange(obj){
				console.log("对比前后，选中的变化")
				console.log(obj)
			}
		}
		
	}
</script>

<style>
</style>
