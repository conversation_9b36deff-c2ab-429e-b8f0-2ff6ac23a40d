/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
const config = {
	
	// 产品名称
	productName: '数字化WMS平台',
	
	// 公司名称
	companyName: '重庆轻企信息技术有限公司',
	
	// 产品版本号
	productVersion: 'V4.3.34',
	
	// 版本检查标识
	appCode: 'android',
	// appCode: 'pda',
	
	// 内部版本号码
	appVersion: 36,
	
	// 更新日期
	appUpdate: '2025/07/30 12:30',
	
	// 管理基础路径
	adminPath: '/a',

	// 整改提交状态
	zgStatus: 2,
	// 整改结果
	zgResult:1,

	/**-1 未进场*/
	STATUS_NOT:'-1',
	/**0 延期中*/
	STATUS_YQ : "0",
	/**1 已通知进场*/
	STATUS_ENTZ: "1",
	/**2 已进场*/
	STATUS_EN:"2",
	/**3 进场通知作废*/
	STATUS_ENZF:"3",
	/**4 已通知撤场*/
	STATUS_EXTZ : "4",
	/**5 已撤场*/
	STATUS_EX:"5",
	// 施工状态 施工中为2
	projStatus:2,
	
	// 保留小数位数
	scaleNum:4, //件数
	scaleQty:4, //数量
	scalePrice:6, //单价
	scaleChangeRate:4, //换算率
	scaleArrQty:4,//收货数
	
	
	MoNotifyPrefix:'sc_',
	PoOrderPrefix:'po_',
	PuArrPrefix:'dh_',
	InventoryPrefix:'inv_',
	PositionPrefix:'hw_',
	TuoPanPrefix:'tp_',
	cbatchPrefix:'pc_',
	MoNotify:1,
	InvBarType:4,
	PosBarType:5,
	TuoPanBarType: 6,
	// needCheckStore检验现存量
	needCheckStore:1,
	jhStatus :{
		NO: 0,
		ING: 1,
		OVER: 2
	},
	deviceType: 2,  //为1是APP，为2是PDA
	deviceTypeAPP : 1,
	deviceTypePDA : 2,
	xiangPrefix: 'X',
	hePrefix: 'H',
	pingPrefix: 'P',
	guanPrefix: 'G',
	fangPrefix: 'F',
	bizType_Fa: 0,
	bizType_Tui: 1,
	PosJustBusTypeEnum: {
		OrtherUp:"1", //其它上架
		OrtherDown:"2", //其它下架
		SoJh:"3", //销售拣货
		OrtherUp:"4", //拣货退回
		OrtherUp:"5", //销售退货
		OrtherUp:"6", //采购入库
		OrtherUp:"7", //采购退货
		Null:"99", //采购退货
	},
	
	
	
	// IP : "**************:9500",
	
	// IP : "xcx.minfeng-chem.cn",
	// IP : "************:8980",
	// IP : "*************:8980",
	// IP : "*************:8980",
	// IP : "*************:8980",
	// IP : "*************:8980",
	// IP : "***************:8980",
	// IP : "*************:8980",
	// IP : "************:8980",
	 IP : "************:8982",
	
	// IP : "wms.ocedu.cn",
	
	
}
// 设置后台接口服务的基础地址
// config.baseUrl = 'http://**************:9500/srm/';
// config.baseUrl = 'http://*************:8980/m8';	
// config.baseUrl = 'http://************:8980/m8';
config.baseUrl = 'http://'+config.IP+'/WMS';
config.xtUrl = 'http://'+config.IP
// config.baseUrl = 'http://**************:8900/srm';
// 建议：打开下面注释，方便根据环境，自动设定服务地址
if (process.env.NODE_ENV === 'development'){
	// config.baseUrl = '/../js'; // 代理模式 vue.config.js 中找到 devServer 设置的地址
	// config.baseUrl = 'http://127.0.0.1:8980/js';
	// config.baseUrl = 'http://************:8980/srm';
	// config.baseUrl = 'http://***************:8980/zfgs';
}

export default config;