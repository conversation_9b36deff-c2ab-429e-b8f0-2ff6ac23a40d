
## 1.1.2（2024-07-01）
1. 新增动画事件
2. 新增车牌号码最后一位新能源特殊样式
3. 处理当输入位数太短时 显示框异常的大
4. 处理震动反馈无效的问题
5. 修复删除异常
## 1.1.1（2023-06-23）

1. 添加示例项目
2. 欢迎添加QQ: 707200833 直接进行反馈



## 1.1.0（2023-04-28）

写在开始, 此版本算是重做了, 但还是保留了 1.0.2 的版本, 所以插件体积包会比较大, 使用时请按需导入.

> 组件说明

| 组件名称          | 组件说明                                 |
| ----------------- | ---------------------------------------- |
| -- 新版本 --      |                                          |
| xm-keyboard-v2    | 1.1.0 新版本                             |
| xm-keyboard-box   | 纯键盘组件                               |
| xm-keyboard-input | 输入框组件                               |
| xm-popup          | 弹出层组件                               |
| xm-square         | 正方形组件，高度可以按照宽度比例进行显示 |
| xm-cell           | 单元格组件                               |
| -- 旧版本 --      |                                          |
| xm-keyboard       | 1.0.2 保留版本                           |



## 1.0.2（2022-12-19）
修复默认值错误


## 1.0.1（2022-12-19）
#### 新增

1. 新增配置 maxAutoClose, 输入达到最大长度后是否自动关闭键盘
2. 新增配置 safeSize, 距离底部的安全距离, 自动匹配失效时可手动配置


#### Bug fixes

- 修复当页面滚动时键盘没有吸底的问题


#### 优化

- 调整为 uni_modules 模式
- 预留键盘底部空间, 优化显示
- 新增默认字体设置
- 按键后顶部进行放大显示
## 1.0.0（2022-01-05）

1. 支持车牌号省份输入
2. 支持数字+字母输入
3. 支持按键震动反馈
4. 支持虚拟键盘显示输入内容
5. 支持默认内容回显
6. 支持是否显示遮罩
7. 支持输入长度限制
8. 支持禁用键盘按键