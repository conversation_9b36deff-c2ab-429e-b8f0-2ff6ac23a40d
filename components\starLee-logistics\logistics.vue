<template>
    <view>
		<view style="background-color: #fff; padding: 20px 10px; margin-bottom: 10px;">
			<u-section :title="title" :right="false" line-color="#00aa00" font-size="32"></u-section>
		</view>
		
		<u-collapse >
				
				<u-collapse-item   v-for="(item1, index1) in listData" :open="index1==0?true:false" :key="item1.wlCode">
					<template #title-all>
						<view class="lee-card lee-flex" style="width: 100%;" v-if="showCard">
						    <slot name="card" :row="item1">
						        <image class="lee-logo" :src="imgUrl(item1.wlNos)" mode="aspectFit"></image>
						        <view class="no">
						            <view class="pd ">
						                <text>物流公司：</text>
						                <text>{{item1.wlComp}}</text>
						            </view>
						            <view>
						                <text>物流单号：</text>
						                <text>{{item1.wlCode}}</text>
						                <text v-if="item1.wlCode" class="copy" @click.stop="copy(item1.wlCode)" :style="{'--pic':mainColor}">复制</text>
						            </view>
						        </view>
						    </slot>
						</view>
					</template>
					<view class="lee-card lee-list-box">
					<u-empty v-if="!item1.wlInfo.traces.length" text="物流信息为空" mode="message"></u-empty>
					<view class="list lee-flex" v-for="(item,index) in item1.wlInfo.traces" :key="index">
					    <view class="left-box">
					        <view class="left">
					            <view class="point" :class="item1.wlInfo.traces.length>=1 && index == 0 ? 'point1':''" :style="{'--pic':opacityColor}">
					                <view class="pint-son" :style="{'--pic':mainColor}"></view>
					            </view>
					            <view v-if="item1.wlInfo.traces.length > 1 && (index + 1) !== item1.wlInfo.traces.length" 
					            :style="{'--pic':mainColor}"
					            class="line" :class="index == 0 ? 'light-line':''"></view>
					        </view>
					    </view>
					    <slot name="process" :row="item">
					        <view class="right" :class="index == 0 ? 'lee-light':'gray'" :style="{'--pic':mainColor}">
					            <!-- <view class="status">{{item[params.status]}}</view>
					            <view class="pd" >{{item[params.content]}}</view>
					            <view class="pd time" :class="index == 0 ? 'lee-light':''" :style="{'--pic':mainColor}">{{item[params.time]}}</view> -->
								<view class="pd" >{{item.acceptStation}}</view>
								<view class="pd time" :class="index == 0 ? 'lee-light':''" :style="{'--pic':mainColor}">{{item.acceptTime}}</view>
					        </view>
					    </slot>
					</view>
					</view>
				</u-collapse-item>
		</u-collapse>
		<view v-if="!listData.length" style="width: 100%;height: 100vh;">
			<u-empty  text="物流信息为空" mode="message"></u-empty>
		</view>
        <!-- <view class="lee-card lee-flex" v-if="showCard">
            <slot name="card" :row="cardInfo">
                <image class="lee-logo" :src="cardInfo[params.src]" mode=""></image>
                <view class="no">
                    <view class="pd ">
                        <text>物流公司：</text>
                        <text>{{cardInfo[params.type]}}</text>
                    </view>
                    <view>
                        <text>物流单号：</text>
                        <text>{{cardInfo[params.no]}}</text>
                        <text class="copy" @click="copy" :style="{'--pic':mainColor}">复制</text>
                    </view>
                </view>
            </slot>
        </view>
        <view class="lee-card lee-list-box">
            <view class="list lee-flex" v-for="(item,index) in list">
                <view class="left-box">
                    <view class="left">
                        <view class="point" :class="list.length>=1 && index == 0 ? 'point1':''" :style="{'--pic':opacityColor}">
                            <view class="pint-son" :style="{'--pic':mainColor}"></view>
                        </view>
                        <view v-if="list.length > 1 && (index + 1) !== list.length" 
                        :style="{'--pic':mainColor}"
                        class="line" :class="index == 0 ? 'light-line':''"></view>
                    </view>
                </view>
                <slot name="process" :row="item">
                    <view class="right" :class="index == 0 ? 'lee-light':'gray'" :style="{'--pic':mainColor}">
						<view class="pd" >{{item.acceptStation}}</view>
						<view class="pd time" :class="index == 0 ? 'lee-light':''" :style="{'--pic':mainColor}">{{item.acceptTime}}</view>
                    </view>
                </slot>
            </view>
        </view> -->
    </view>
</template>

<script>
    export default {
        props:{
			orderCode:{
				type:String,
				default(){
				    return ''
				}
			},
            // 物流数据列表
            list:{
                type:Array,
                default(){
                    return []
                }
            },
            // 数据默认字段
            params:{
                type:Object,
                default(){
                    return {src:'src',type:'type',no:'no',status:'status',content:'content',time:'time'}
                }
            },
            // 是否显示物流卡片信息
            showCard:{
                type:Boolean,
                default(){
                    return true
                }
            },
            // 物流卡片信息
            cardInfo:{
                type:Object,
                default(){
                    return {
                        src:'https://t10.baidu.com/it/u=996032835,1968172858&fm=58',
                        type:'韵达速递',
                        no:'YD34592423445154'
                    }
                }
            },
            // 物流卡片信息-是否显示复制按钮
            showCopy:{
                type:Boolean,
                default(){
                    return true
                }
            },
            // 主题颜色
            color:{
                type:String,
                default(){
                    return '#ff5a07'
                }
            },
        },
		watch: {
			orderCode(nVal, oVal) {
				let that = this
				// this.$u.api.shop.wlInfo({
				// 		soCode: nVal
				// 	})
				// 	.then(res => {
				// 		// this.dataList = res.traces.reverse()
				// 		// this.dataList = res.map(item=>{
				// 		// 	if(item.Traces){
				// 		// 		item.Traces = item.Traces.reverse()
				// 		// 	}
				// 		// 	return item
				// 		// })
				// 		this.listData = res.map(item=>{
				// 			item.Traces = []
				// 			return item
				// 		})
				// 		console.log(res, 'res ===');
				// 	});
				this.$u.api.shop.wlTest({
						OrderCode: nVal
					})
					.then(res => {
						
						this.listData = res.map(item=>{
							if(item.wlInfo.traces.length){
								item.wlInfo.traces = item.wlInfo.traces.reverse()
							}
							return item
						})
						this.title = `共有${this.listData.length}个物流信息`
						// this.listData = res
						console.log(res, 'res ===');
					});
					
					
			}
		},
		
        computed:{
            mainColor(){
                return this.set16ToRgb(this.color)
            },
            opacityColor(){
                 return this.set16ToRgb(this.color,0.5)
            }
        },
        data() {
            return {
				listData:[]	,
				flag:'',
				title:'共有0个物流信息',
            }
        },
		
        methods: {
			change(item,index){
				console.log(item,index);
				this.flag = index
					this.$u.api.shop.orderInfo({
							OrderCode: this.orderCode,
							ShipperCode:item.wlNos,
							LogisticCode:item.wlCode,
						})
						.then(res => {
							if(!res.traces.length){
								this.$u.toast(res.reason);
							}
							this.listData[index].Traces = res.traces.reverse()
							let arr = [...this.listData]
							this.listData = [] 
							setTimeout(()=>{
								this.listData = [...arr]
							})
							// this.listData = this.listData.map((req,i) => {
							// 	if (index == i) {
							// 		req.Traces = res.traces.reverse()
							// 	}
							// 	return req
							// })
							// this.listData
							console.log(this.listData);
							
						
						});
				
			},
			imgUrl(code){
				let url = ''
				if(code =='ANEKY' || code =='BTWL' || code =='DBLKY' || code =='JD' || code =='SF'
					|| code =='SX' || code =='YMDD' || code =='ZTO' || code =='ZTOKY')
				{
					url = `/static/kd/${code}.png`
				}else{
					url = '/static/kd/WU.png'
				}
			    return url;
			},
            // 复制
            copy(code){
                uni.setClipboardData({
                	data: code,
                	success: ()=> {}
                });
            },
            // 16进制转rgb/rgba
            set16ToRgb(str,opacity){
               var reg = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/
               if(!reg.test(str)){return;}
               let newStr = (str.toLowerCase()).replace(/\#/g,'')
               let len = newStr.length;
               if(len == 3){
                   let t = ''
                   for(var i=0;i<len;i++){
                       t += newStr.slice(i,i+1).concat(newStr.slice(i,i+1))
                   }
                   newStr = t
               }
               let arr = []; //将字符串分隔，两个两个的分隔
               for(var i =0;i<6;i=i+2){
                   let s = newStr.slice(i,i+2)
                   arr.push(parseInt("0x" + s))
               }
               let res = opacity ? `rgb(${arr.join(",")},${opacity})` : `rgb(${arr.join(",")})`
               return res;
            }
        }
    }
</script>

<style lang="scss" scoped>
$eee:#eee;
.lee-flex{
    display: flex;
}
.gray{
    color: #9f9f9f;
}
.lee-light{
    color: var(--pic)!important;
}
.lee-card{
    margin: auto;
    padding: 25rpx 45rpx;
    border-radius: 10rpx;
    background: white;
    .no{
        font-size: 28rpx;
    }
    .copy{
        background: var(--pic);
        color: white;
        padding: 4rpx 14rpx;
        font-size: 28rpx;
        border-radius: 10rpx;
        margin-left: 20rpx;
    }
    .lee-logo{
        width: 90rpx;
        height: 90rpx;
        margin-right: 30rpx;
    }
    .pd{
        padding-bottom: 20rpx;
    }
}
.lee-list-box{
    padding: 5rpx 35rpx;
    .list{
        padding-top: 30rpx;
        position: relative;
        .left-box{
            .left{
                width: 50rpx;
                .point{
                    z-index: 10;
                    width: 30rpx;
                    height: 30rpx;
                    border-radius: 50%;
                    margin-left: calc(20rpx / 2);
                    margin-top: 5rpx;
                    background: $eee;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    
                }
                .point1{
                    background: var(--pic);
                    margin-top: 0;
                    margin-left: 0rpx;
                    width: 50rpx;
                    height: 50rpx;
                    color: white;
                    .pint-son{
                        width: 60%;
                        height: 60%;
                        background: var(--pic);
                        border-radius: 50%;
                    }
                }
                .line{
                    display: flex;
                    justify-content: center;
                }
                .line::after{
                    content: '';
                    position: absolute;
                    background: $eee;
                    height: calc(100% - 30rpx);
                    width: 4rpx;
                    // margin-top: 10rpx;
                }
                .light-line::after{
                    background: var(--pic);
                }
            }
        }
        
        .right{
            padding-left: 15rpx;
            .status{
                font-weight: bold;
            }
            .time{
                color: #8b8b8b;
                font-size: 28rpx;
            }
        }
    }
    .list:last-child{
        padding-bottom: 30rpx;
    }
}

</style>
