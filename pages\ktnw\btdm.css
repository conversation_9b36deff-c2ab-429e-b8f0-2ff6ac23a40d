page {
	background-color: #f8f8f8;
}
.btn-plus {
	position: absolute;
	bottom: 50rpx;
	right: 50rpx;
	z-index: 1;
	opacity: 0.6;
}
.btn-plus:hover {
	opacity: 1;
}

.fiex{
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
}

.margin-top-xxl{
	margin-top: 45px;
}

/* 底部栏 */
	.action-section{
		/* #ifdef H5 */
		/* #endif */
		position:fixed;
		left: 35%;
		bottom:30upx;
		z-index: 95;
		display: flex;
		align-items: center;
		background: rgba(255,255,255,.9);
		border-radius: 40upx;
		.checkbox{
			height:52upx;
			position:relative;
			image{
				width: 52upx;
				height: 100%;
				position:relative;
				z-index: 5;
			}
		}
		.clear-btn{
			position:absolute;
			left: 26upx;
			top: 0;
			z-index: 4;
			width: 0;
			height: 52upx;
			line-height: 52upx;
			padding-left: 38upx;
			font-size: $font-base;
			color: #fff;
			background: $font-color-disabled;
			border-radius:0 50px 50px 0;
			opacity: 0;
			transition: .2s;
			&.show{
				opacity: 1;
				width: 120upx;
			}
		}
		.total-box{
			flex: 1;
			display:flex;
			flex-direction: column;
			text-align:right;
			padding-right: 40upx;
			.price{
				font-size: $font-lg;
				color: $font-color-dark;
			}
			.coupon{
				font-size: $font-sm;
				color: $font-color-light;
				text{
					color: $font-color-dark;
				}
			}
		}
		.confirm-btn{
			padding: 0 38upx;
			margin: 0;
			border-radius: 100px;
			height: 76upx;
			line-height: 76upx;
			font-size: $font-base + 2upx;
			background: $uni-color-primary;
		}
	}

	.indexBar {
		position: fixed;
		right: 0px;
		bottom: 0px;
		padding: 20upx 20upx 20upx 60upx;
		display: flex;
		align-items: center;
	}

	.indexBar .indexBar-box {
		width: 40upx;
		height: auto;
		background: #fff;
		display: flex;
		flex-direction: column;
		box-shadow: 0 0 20upx rgba(0, 0, 0, 0.1);
		border-radius: 10upx;
	}

	.indexBar-item {
		flex: 1;
		width: 40upx;
		height: 40upx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24upx;
		color: #888;
	}

	movable-view.indexBar-item {
		width: 40upx;
		height: 40upx;
		z-index: 9;
		position: relative;
	}

	movable-view.indexBar-item::before {
		content: "";
		display: block;
		position: absolute;
		left: 0;
		top: 10upx;
		height: 20upx;
		width: 4upx;
		background-color: #f37b1d;
	}

	.indexToast {
		position: fixed;
		top: 0;
		right: 80upx;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		width: 100upx;
		height: 100upx;
		border-radius: 10upx;
		margin: auto;
		color: #fff;
		line-height: 100upx;
		text-align: center;
		font-size: 48upx;
	}
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.sv {
		height: 55vh;
		overflow-y: auto;
		width: 100%;
	}
	
	.sh {
		height: 75vh;
		overflow-y: auto;
		width: 100%;
	}
	.sg {
		height: 79vh;
		overflow-y: auto;
		width: 100%;
	}
	.item-container {
		width: 100%;
		background-color: white;
		height: 45px;
		display: flex;
		align-items: center;
		padding: 10rpx;
		margin: 10rpx 10rpx 0rpx 10rpx;
	}
	
	.btn {
		width: 100%;
		margin-top: 10px;
	}
	
	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}
	
	.text-area {
		display: flex;
		justify-content: center;
	}
	
	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
	
	.start {
		height: 30px;
		line-height: 30px;
		font-size: 12px;
		background-color: #007AFF;
		color: white;
		margin-right: 10rpx;
	}
	
	.stop {
		height: 30px;
		line-height: 30px;
		font-size: 12px;
		background-color: #ff0000;
		color: white;
	}
	.footer{
	  position: fixed;
	  bottom: 0;
	  width: 100%;
	  line-height: var(--footer-height);
	  background: #ffffff;
	  color: #ffffff;
	}
	