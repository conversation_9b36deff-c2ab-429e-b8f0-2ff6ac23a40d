<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<!-- <u-sticky class="u-sticky">
			<view class="cu-bar search" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
					@search="confirm"></u-search>
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="search" name="scan" size="50"></u-icon>
				</view>
			</view>
		</u-sticky> -->
		<u-form class="form bg-white" :model="model" ref="uForm" label-position="left">
			<u-form-item label="公司:" prop="companyCode" label-width="100" required>
				<js-select v-model="model.companyCode" :showFilter="false" :items="companySelectList" placeholder="请选择"
					:tree="true" :label-value="model['company.companyName']"
					@label-input="model['company.companyName'] = $event" @confirm="selectcompanyConfirm"></js-select>
			</u-form-item>
			<!-- <u-form-item label="仓库:" prop="whCode" label-width="100" required>
				<js-select v-model="model.whCode" :showFilter="false" :items="whSelectList" placeholder="请选择"
					:tree="true" :label-value="model['basWare.cwhname']"
					@label-input="model['basWare.cwhname'] = $event" @confirm="selectConfirm"></js-select>
			</u-form-item> -->
			<view style="display: flex;">
				<u-form-item label="客户:" prop="cusCode" label-width="100" required  >
					<js-select v-model="model['cusCode']" :showFilter="false" dict-type="wms_cus"
						placeholder="请选择"></js-select>
				</u-form-item>
				<u-form-item label="仓库:" prop="whCode" label-width="100" style="margin-left:20px;">
					<js-select v-model="model.whCode" :showFilter="false" :disabled="true" :items="whSelectList" placeholder="请选择"
						:tree="true" :label-value="model['basWare.cwhname']"
						@label-input="model['basWare.cwhname'] = $event" @confirm="selectConfirm"></js-select>
				</u-form-item>
			</view>
			<!-- <u-form-item label="默认货位:" prop="defPosName" label-width="180">
				<view style="display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
					<view v-if="model.defPosName" style="margin-right: 10rpx;">{{model.defPosName}}</view>
				</view>
			</u-form-item> -->
			<!-- <u-form-item label="默认货位:" prop="defPosName" label-width="180">
				<view @click="xzhw" style="display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
					<view v-if="model.defPosName" style="margin-right: 10rpx;">{{model.defPosName}}</view>
					<view v-if="!model.defPosName" style="margin-right: 10rpx;color: #eee;">请扫码或选择货位</view>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</u-form-item> -->


			<u-form-item label="备注:" prop="remarks" label-width="100">
				<u-input placeholder="请输入" v-model="model['remarks']" type="text" maxlength="180"></u-input>
			</u-form-item>
			<u-form-item label="合计:" prop="remarks" label-width="100">
				{{  sumTh  }}
			</u-form-item>


		</u-form>
		<view class="action bg-white " style="display: flex;justify-content: space-between;">
			<view style="color: #3E97B0;font-size: 20px;align-items: center;display: flex;padding: 0 0 10px 10px;">
				<!-- <u-icon name="/static/image/detail.png" size="80"></u-icon> -->
				<text class="cuIcon-titles text-bold">退货明细</text>
			</view>
			<view style="padding: 10px;" @click="addsp">
				<u-icon name="/static/image/xz.png" size="70"></u-icon>
			</view>
		</view>

		<view style="padding: 10px;">
			<view v-for="(item,index) in children" :id="'id'+ item.id" class="cu-item shadow "
				style="position: relative;" :key="index">
				<view class="cu-form-group" style="display: flex;justify-content: space-between;">
					<view
						style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
						{{ index + 1 }}
					</view>
					<view>
						<text style="font-size: 35px;" class="cuIcon-deletefill text-sl text-red"
							@tap="delsoOutDetail(item,index)"></text>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">商品：</view>
					<view style="flex: 1;"> {{ item.basInv?item.basInv.invName:""  }} </view>
				</view>
				
				<view class="cu-form-group">
					<view class="title">货位：</view>
					
					<view 
						style="flex: 1;display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
						<view v-if="item.basPosition.posName" style="margin-right: 10rpx;">{{item.basPosition.posName}}
						</view>
					</view>
					<!-- <view @click="xzhw2(item,index)"
						style="flex: 1;display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
						<view v-if="item.basPosition.posName" style="margin-right: 10rpx;">{{item.basPosition.posName}}
						</view>
						<view v-if="!item.basPosition.posName" style="margin-right: 10rpx;color: #eee;">请选择货位</view>
						<u-icon name="arrow-right"></u-icon>
					</view> -->
				</view>
				<view class="cu-form-group">
					<view class="title"><text class="cuIcon-favorfill text-xs text-red"></text>数量：</view>
					<view style="flex: 1;">
						<u-input placeholder="请输入" v-model="item.iqty" type="number"
							@input="replaceInput($event,item,index)" clearable maxlength="64"></u-input>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">形象刊：</view>
					<view style="flex: 1;">
						<u-input placeholder="请输入" v-model="item.freeVO.cfree1" type="text" di maxlength="64"></u-input>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">备注：</view>
					<view style="flex: 1;">
						<u-input placeholder="请输入" v-model="item.remarks" type="text" di maxlength="64"></u-input>
					</view>
				</view>

			</view>

		</view>
		<!-- @cancel="cancel" -->
		<u-modal v-model="thshow" title="新增退货明细" @confirm="tjconfirm" :negative-top="570" :show-cancel-button="true" width="80%" >
			<view class="cu-form-group">
				<view class="title">商品：</view>
				<view style="flex: 1;"> {{ thData.basInv?thData.basInv.invName:""  }} </view>
			</view>
			
			<view class="cu-form-group">
				<view class="title">货位：</view>
				<!-- <view @click="xzhw2(thData,999)"
					style="flex: 1;display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
					<view v-if="thData.basPosition.posName" style="margin-right: 10rpx;">{{thData.basPosition.posName}}
					</view>
					<view v-if="!thData.basPosition.posName" style="margin-right: 10rpx;color: #eee;">请选择货位</view>
					<u-icon name="arrow-right"></u-icon>
				</view> -->
				
				<view 
					style="flex: 1;display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
					<view v-if="thData.basPosition.posName" style="margin-right: 10rpx;">{{thData.basPosition.posName}}
					</view>
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title"><text class="cuIcon-favorfill text-xs text-red"></text>数量：</view>
				<view style="flex: 1;">
					<u-input placeholder="请输入" v-model="thData.iqtyNumber" type="number" :focus="iqtyfocus"
						@input="replaceInputIqty($event,thData)" clearable maxlength="64"></u-input>
				</view>
				<view>已拣数：</view>
				<view style="flex: 1;">
					{{ thData.iqty }}
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title">形象刊：</view>
				<view style="flex: 1;">
					<u-input placeholder="请输入" v-model="thData.freeVO.cfree1" type="text" di maxlength="64"></u-input>
				</view>
			</view>
			<view class="cu-form-group">
				<view class="title">备注：</view>
				<view style="flex: 1;">
					<u-input placeholder="请输入" v-model="thData.remarks" type="text" di maxlength="64"></u-input>
				</view>
			</view>
		</u-modal>
		

		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="submit" type="primary"
						style="width: 90px; height: 70px; color: #fff; font-size: 14px">本次拣货完成
					</u-button>
				</movable-view>
			</movable-area>
		</view>
		<!-- <view>
			<movable-area class="movable-area1">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="handleFocus" type="success"
						style="width: 90px; height: 70px; color: #fff; ">
						<view style="font-size: 16px;display: flex; flex-direction: column;text-align: center;">
							<u-icon style="margin: auto;" name="/static/jeesite/sm1.png" size="80"></u-icon>
							<view>扫一扫</view>
						</view>
					</u-button>

				</movable-view>
			</movable-area>
		</view> -->
	</view>
</template>

<script>
	import config from '@/common/config.js';
	export default {
		data() {
			return {
				back:true,
				companySelectList: [],
				whSelectList: [],
				thData: {
					basPosition:{},
					basInv: {},
					freeVO: {},
				},
				thshow: false,
				jhshow: false,
				barCode: "",
				jhQtyFocus: false,
				focus: false,
				iqtyfocus: false,
				shQty: '',
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				invFlag: false,
				model: {
					basInv: {

					},
					freeVO: {

					},
				},
				//主表id
				query: {
					id: '',
				},
				//子表id
				querys: {
					id: '',
				},
				children: [],
				carVenSelectList: [],
				pickerTime: false, //控制日期显示
				currentCposCode: {},
				currentThCposCode: {},
				x: 650, //x坐标
				y: 650, //y坐标
				thFocus: false,
				paramsId: '',
				sumTh: 0,
				tjSubmitting: false ,
			}
		},
		onLoad() {
			
			this.loadList({
				bred: '0',
				busType: '3',
			});
			this.$u.api.ktnw.companyTreeData().then(res => {
				this.companySelectList = res;
			});

			this.$u.api.ktnw.basWarehouseTreeData().then(res => {
				this.whSelectList = res;
			});

			uni.$on('hwObjs', (arr) => {

				if (arr.length) {
					if (!arr[0]['type']) {
						this.model.defPos = arr[0].id
						this.model.defPosName = arr[0].name
						this.children.forEach(item => {
							item.posCode = arr[0].id
							// item.basPosition.posName = arr[0].name
							item.basPosition = {
								posName: arr[0].name,
							}
						})
						this.$forceUpdate()
					}else if(arr[0]['type'] == 999){
						this.thData.posCode = arr[0].id
						this.thData.basPosition = {
							posName: arr[0].name,
						}
						this.$forceUpdate()
					}else {
						this.children.forEach((item, index) => {
							if (index == arr[0]['type']) {
								item.posCode = arr[0].id
								// item.basPosition.posName = arr[0].name
								item.basPosition = {
									posName: arr[0].name,
								}
							}
						})
						this.$forceUpdate()
					}

				}
			})
			
			uni.$on('xzsp', (item) => {
				let _that = this;
				_that.iqtyfocus = false;
				setTimeout(() => {
					_that.iqtyfocus = true;
				}, 500)
				
				this.thshow = true
				// 判断是否是新增 this.children的 invCode 里面是否包含 item.id
				
				console.log(flag, 'flag')

				this.thData = {
					id: new Date().getTime(),
					isNewRecord: true,
					// editable: true,
					invCode: item.invCode,
					companyCode: item.companyCode,
					basInv: {
						viewCode: item.viewCode,
						invName: item.invName,
						funitname: item.funitname,
					},
					parent: {
						company: {
							companyName: item.company.companyName,
						},
					},
					posCode: this.model.defPos || '',
					basPosition: {
						posName: this.model.defPosName || '',
					},
					freeVO: item.freeVO || {},
					// iqty: -1
				}
				let flag = this.children.findIndex(itemInvCode => itemInvCode.invCode == item.id)
				if (flag != -1) {
					this.thData.iqty = this.children[flag]['iqty'] || 0
					this.thData.iqtycs = this.children[flag]['iqty'] || 0
				} else {
					this.thData.iqty = 0
					this.thData.iqtycs = 0
				}

				this.$forceUpdate()
			})

			// uni.$on('spObjs', (data) => {
			// 	console.log(data, 123);
			// 	data.forEach((item, index) => {
			// 		this.children.unshift({
			// 			id: new Date().getTime() + index,
			// 			isNewRecord: true,
			// 			// editable: true,
			// 			invCode: item.invCode,
			// 			companyCode: item.companyCode,
			// 			basInv: {
			// 				viewCode: item.viewCode,
			// 				invName: item.invName,
			// 				funitname: item.funitname,
			// 			},
			// 			// cfree2: item.weight,
			// 			parent: {
			// 				company: {
			// 					companyName: item.company.companyName,
			// 				},
			// 			},
			// 			posCode: this.model.defPos || '',
			// 			basPosition: {
			// 				posName: this.model.defPosName || '',
			// 			},
			// 			freeVO: item.freeVO || {},
			// 			iqty: -1
			// 		})
			// 	})

			// })
		},
		watch: {
			children(val) {
				console.log(val, 'val')
				this.sumTh = val.reduce((sum, item) => sum + (parseFloat(item.iqty) || 0), 0);
			}
		},
		onShow() {
			// console.log('页面开启（onLoad）广播监听事件：xwscan')
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		computed: {
			sumTh(){
		// 		updateSumTh() {
		// 	this.sumTh = this.children.reduce((sum, item) => sum + (parseFloat(item.iqty) || 0), 0);
		// }
				return this.children.reduce((sum, item) => sum + (parseFloat(item.iqty) || 0), 0);
			}
		},
		methods: {
			onBackPress() {
				let that = this
				if(this.back && this.children.length){
					uni.showModal({
					  title: '是否确定退出？',
					  content: '退出会清空全部退货数据，请确认？',
					  success: function (res) {
					    if (res.confirm) {
							that.back = false
							setTimeout(()=>{
								uni.navigateBack({
									delta: 1
								})
							},500)
					    } else if (res.cancel) {
							that.back = true
					    }
					  }
					});
					return true;
				}
			     // 返回true表示已经处理了返回事件，不再执行默认的返回逻辑。
			},
			delsoOutDetail(item, index) {
				console.log('item', item)
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除该明细？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
						// const filteredChildren = that.children.filter(listItem => listItem.tuopan !== item.cbatch);
						// that.children = filteredChildren;
						that.children.splice(index, 1);
						that.$forceUpdate()
					}
				})
			},
			handleFocus() {
				console.log('focus', this.focus)
				this.focus = false;
				setTimeout(() => {
					this.focus = true;
				}, 500)
				console.log('focus', this.focus)
			},
			selectcompanyConfirm() {
				this.children = []
				this.$forceUpdate()
			},
			selectConfirm() {
				this.model.defPos = ''
				this.model.defPosName = ''
				this.children.forEach(item => {
					item.posCode = ''
					item.basPosition.posName = ''
				})
				this.$forceUpdate()
			},
			replaceInput(e, item, index) {
				if (e > 0) {
					item.iqty = -e
				} else {
					item.iqty = e
				}
				this.$forceUpdate()
				// console.log(e);
				// var that = this
				// e = e.match(/^\d*(\.?\d{0,2})/g)[0]
				// this.$nextTick(() => {
				// 	that.model.iqty = e
				// })
			},
			replaceInputIqty(e, item) {
                console.log('replaceInputIqty', e,e === '');
                // 若输入为空，重置已拣数为 thData.iqtycs 的值
                if (e === '') {
                    item.iqty = this.thData.iqtycs;
                    this.$forceUpdate();
                    return;
                }
                // 将输入转换为数字
                const inputNumber = Number(e);
                if (!isNaN(inputNumber)) {
                    console.log('item.iqty 初始值:', item.iqty);
                    console.log('inputNumber 的值:', inputNumber);
                    // 正确处理 item.iqty 的累加，初始值为 this.thData.iqtycs
                    // item.iqty = this.thData.iqtycs + inputNumber;
					item.iqty = Math.abs(this.thData.iqtycs) + Math.abs(inputNumber);
					console.log('iqty',item.iqty)
					if (e > 0) {
						item.iqty = -item.iqty,
						item.iqtyNumber = -e
					} else {
						item.iqty = -item.iqty
						item.iqtyNumber = e
					}
                }
                this.$forceUpdate();
            },
			tjconfirm(){ // 原方法开始
				// this.children.unshift(this.thData)
				let flag = this.children.findIndex(itemInvCode => itemInvCode.invCode == this.thData.invCode)
				if (flag != -1) {
					this.children[flag].iqty = this.thData.iqty
					this.sumTh = this.children.reduce((sum, item) => sum + (parseFloat(item.iqty) || 0), 0);
					this.$forceUpdate()
				} else {
					this.children.unshift(this.thData)
				}
				this.$forceUpdate()
			}, // 原方法结束
			invCode(bar, companyCode) {
				let InventoryPrefix = this.vuex_config.InventoryPrefix;
				if (bar.indexOf(InventoryPrefix) != -1) {
					return bar
				} else {
					let code = `inv_${companyCode}_${bar}`
					return code
				}
			},

			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					this.confirm()
					//将换行符分割成数组
					// const allItems = newString.split('\n');
					// 	//遍历数组，将每一项添加到arr中
					// 	for(let i = 0;i<allItems.length;i++){
					// 		this.arr.push({
					// 			"content":allItems[i],
					// 			"remarks":this.remarks
					// 		})
					// 	}
				}
			},
			confirm() {
				let _that = this;
				_that.focus = false
				let InventoryPrefix = _that.vuex_config.InventoryPrefix;
				let PositionPrefix = _that.vuex_config.PositionPrefix;
				let InvBarType = _that.vuex_config.InvBarType;
				let PosBarType = _that.vuex_config.PosBarType;
				let bar = encodeURIComponent(this.barCode)

				// if (bar.indexOf(PositionPrefix) != -1) {
				// 	this.$u.api.ktnw.getBarInfo({
				// 		barCode: bar,
				// 		barType: PosBarType,
				// 		whCode: this.model.whCode || ''
				// 	}).then((res) => {
				// 		if (res.result == 'true') {
				// 			_that.sendMp3('cg');
				// 			setTimeout(() => {
				// 				_that.focus = true;
				// 			}, 500)
				// 			this.model.whCode = res.data.basWare.cwhcode
				// 			this.model.defPos = res.data.basPos.posCode
				// 			this.model.defPosName = res.data.basPos.posName
				// 			this.children.forEach(item => {
				// 				item.posCode = res.data.basPos.posCode
				// 				item.basPosition = {
				// 					posName: res.data.basPos.posName,
				// 				}
				// 			})
				// 			this.$forceUpdate()

				// 		} else {
				// 			_that.sendMp3('sb');
				// 			let message = res.message
				// 			_that.$refs.jsError.showError("", message, "error");
				// 			setTimeout(() => {
				// 				_that.focus = true;
				// 			}, 500)
				// 		}
				// 		this.barCode = ''
				// 	})
				// } else {
				// }
				if (!this.model.companyCode) {
					_that.sendMp3('sb');
					_that.$refs.jsError.showError("", "请先选择公司", "error");
					setTimeout(() => {
						_that.focus = true;
						_that.barCode = ''
					}, 500)
					return;
				}
				let barCode = this.invCode(bar, this.model.companyCode)
				console.log('barCode',barCode)
				this.$u.api.ktnw.getBarInfo({
					barCode: barCode,
					barType: InvBarType,
					companyCode: this.model.companyCode
				}).then((res) => {
					if (res.result == 'true') {
						if (!res.data.basInv) {
							_that.sendMp3('sb');
							let message = '请扫描正确的商品码'
							_that.$refs.jsError.showError("", message, "error");
							setTimeout(() => {
								_that.barCode = ''
								_that.focus = true;
							}, 500)
							return;
						}
						
						let item = res.data.basInv
						console.log('item',item)
						if (item.companyCode == this.model.companyCode) {
							_that.sendMp3('cg');
							_that.iqtyfocus = false;
							setTimeout(() => {
								_that.barCode = ''
								_that.iqtyfocus = true;
							}, 500)
							this.thshow = true
							this.thData = {
								id: new Date().getTime(),
								isNewRecord: true,
								// editable: true,
								invCode: item.invCode,
								companyCode: item.companyCode,
								basInv: {
									viewCode: item.viewCode,
									invName: item.invName,
									funitname: item.funitname,
								},
								parent: {
									company: {
										companyName: item?.company?.companyName,
									},
								},
								posCode: this.model.defPos || '',
								basPosition: {
									posName: this.model.defPosName || '',
								},
								freeVO: item.freeVO || {},
								// iqty: -1
							}
							let flag = this.children.findIndex(itemInvCode => itemInvCode.invCode == item.id)
							if (flag != -1) {
								this.thData.iqty = this.children[flag]['iqty'] || 0
								this.thData.iqtycs = this.children[flag]['iqty'] || 0
							} else {
								this.thData.iqty = 0
								this.thData.iqtycs = 0
							}
							
							this.$forceUpdate()
						} else {
							_that.sendMp3('sb');
							setTimeout(() => {
								_that.barCode = ''
								_that.focus = true;
							}, 500)
							let message = '请扫描对应公司商品码'
							_that.$refs.jsError.showError("", message, "error");
						}
				
					} else {
						_that.sendMp3('sb');
						let message = res.message
						_that.$refs.jsError.showError("", message, "error");
						setTimeout(() => {
							_that.barCode = ''
							_that.focus = true;
						}, 500)
					}
					this.barCode = ''
				
				})

			},
			search() {
				var _that = this;
				_that.focus = false
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			xzhw() {
				if (!this.model.whCode) {
					this.$u.toast('请先选择仓库！');
					return;
				}
				let whcode = this.model.whCode || ''
				uni.navigateTo({
					url: "/pages/ktnw/qtsj/hwXz?whcode=" + whcode,
				});
			},
			xzhw2(item, index) {
				if (!this.model.whCode) {
					this.$u.toast('请先选择仓库！');
					return;
				}
				let whcode = this.model.whCode || ''
				uni.navigateTo({
					url: "/pages/ktnw/qtsj/hwXz?whcode=" + whcode + '&type=' + index,
				});
			},
			addsp() {
				if (!this.model.companyCode) {
					this.$u.toast('请先选择公司！');
					return;
				}
				// uni.navigateTo({
				// 	url: "/pages/ktnw/xsth/xzspList?companyCode=" + this.model.companyCode,
				// });
				
				uni.navigateTo({
					url: "/pages/ktnw/qtsj/xzspList?companyCode=" + this.model.companyCode,
				});
			},
			paste() {
				let that = this
				uni.getClipboardData({
					success: (res) => { // 获取成功回调
						that.model.carNo = res.data.trim().slice(0, 8)
						that.$forceUpdate()
						if (!that.model.cdriver && !that.model.driverPhone) {
							that.$u.api.mffh.getCarInfoByCarNo({
								carNo: that.model.carNo
							}).then(res => {
								that.model.cdriver = res.data.cdriver || '';
								that.model.driverPhone = res.data.driverPhone || '';
								that.$forceUpdate()
							});
						}
					}
				})
			},
			// cancel() {
			// 	// uni.$emit('refreshData');
			// 	uni.navigateBack({
			// 		delta: 1,
			// 	})
			// },			
			loadList(data) {
				this.$u.api.ktnw.rd02Form(data).then(res => {
					this.model = res.rd02
					this.model.companyCode = this.vuex_company.companyCode || '';
					this.model.whCode = '99';
					this.model.cusCode = '100048';
					this.model.defPos = '20001';
					this.model.defPosName = '退货仓中转货位-上架';
					
					// this.model.defPos = '18001';
					// this.model.defPosName = '上架中转货位';
					
					this.$forceUpdate()
				})
			},

			submit() {

				if(this.tjSubmitting) {
					this.$u.toast('正在处理中，请稍候...');
					return;
				}

				if (!this.model.companyCode) {
					this.$refs.jsError.showError("", "请先选择公司", "error");
					return;
				}
				if (!this.model.whCode) {
					this.$refs.jsError.showError("", "请先选择仓库", "error");
					return;
				}
				if (!this.model.cusCode) {
					this.$refs.jsError.showError("", "请先选择客户", "error");
					return;
				}

				if (!this.children.length) {
					this.$refs.jsError.showError('', '请先添加退货明细！', 'error');
					return;
				}
				let flagIqty = true
				let flagPosCode = true

				this.children.forEach(item => {
					if (!item.iqty) {
						flagIqty = false
					}
					if (!item.posCode) {
						flagPosCode = false
					}
				})
				if (!flagIqty) {
					this.$refs.jsError.showError('', '请输入退货明细的数量！', 'error');
					return;
				}
				if (!flagPosCode) {
					this.$refs.jsError.showError('', '请选择退货明细的货位！', 'error');
					return;
				}

				// 设置提交状态
				this.tjSubmitting = true;

				try {
					this.$u.api.ktnw.rd02Save({
						...this.model,
						status:'9',
						rds02List: this.children,
					}).then(res => {
						if (res.result == 'true') {
							this.$u.toast(res.message);
							this.back = false
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								})
							}, 500)
						} else {
							this.$refs.jsError.showError('', res.message, 'error');
						}
					});
				} catch (err) {
					// 隐藏加载提示
					uni.hideLoading();
					console.error('处理失败:', err);
					this.$refs.jsError.showError("", "网络请求失败，请检查网络连接后重试", "error");
				} finally {
					// 重置提交状态
					this.tjSubmitting = false;
				}


			},
			sendMp3(name) {
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
		},
	}
</script>
<style lang="scss" scoped>
	/* .footer {
	position: fixed;
	bottom: 0;
	right:0;
	width: 100%;
	line-height: var(--footer-height);
	background: #ffffff;
	color: #ffffff;
} */
	$all_width: 96rpx;
	$all_height: 96rpx;

	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.btn-plus {
		position: fixed;
		bottom: 260rpx;
		right: 40rpx;
		z-index: 2;
		opacity: 0.6;
	}

	.btn-plus:hover {
		opacity: 1;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */

	}

	.cu-bar {
		min-height: 80px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 160rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}


	.cu-modal-footer {
		padding: 32rpx 32rpx !important;
		width: 100%;

		.cu-btn {
			width: 50%;
		}

	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;
		z-index: 999;
		border-top: 1px solid #eee;
	}

	/deep/ .u-model__title[data-v-3626fcec] {
		color: #fff !important;
		padding: 10px !important;
		background: #3e97b0 !important;
		font-weight: bold !important;
	}
</style>