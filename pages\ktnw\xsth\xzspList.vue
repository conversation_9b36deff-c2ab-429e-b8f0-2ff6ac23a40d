<template> <!-- 发货记录-->
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<view class="search">
			<u-search v-model="query.invName" :show-action="false" @search="loadList" placeholder="输入商品名称搜索" ></u-search>
		</view>
		<scroll-view class="scroll-list" scroll-y="true" @scrolltolower="loadMore" style="padding: 10px;">
			<u-checkbox-group v-model="value" style="width: 100%;"  @change="checkboxChange">
			<view v-for="(item, index) in fhList" :key="index" @click="addData(item,index)" class=" bg-white" style="width: 100%;position: relative;border-radius: 10px;margin-bottom: 10px;border: 1px solid #eee;overflow: hidden;">
				<view class="cu-form-group" style="display: flex;justify-content: space-between;">
					<view class="">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<!-- <text class="text padding-left-lg text-red">
							 {{ item.cposName|| ""  }} {{ item.cposCode ? '('+ item.cposCode +')' : '' }}
						</text> -->
					</view>
					<view class="">
						<!-- <u-checkbox class="rights" :name="index" :size="50" :customStyle="{marginBottom: '8px'}" activeColor="red"></u-checkbox>	 -->
					<!-- v-if="selectParent?true:item.lastRank" @tap.stop="_treeItemSelect(item, index)" -->
					<view class="next-tree-check">
						<view class="next-tree-check-yes" v-if="item.checked" :class="{'radio':!multiple}"
							:style="{'border-color':confirmColor, 'background-color':confirmColor}">
							<view class="next-tree-check-yes-b" :style="{'background-color':confirmColor}">
								<text class="icon-text">✔</text>
							</view>
						</view>
						<view class="next-tree-check-no" v-else :class="{'radio':!multiple}" :style="{'border-color':confirmColor}"></view>
					</view>
					</view>
				</view>
				
				<view class="cu-form-group">
					<view class="title">公司：</view>
					<view style="flex: 1;"> {{ item.company?item.company.companyName:""  }}
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">商品编码：</view>
					<view style="flex: 1;"> {{ item.viewCode|| ""  }}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">商品名称：</view>
					<view style="flex: 1;"> {{ item.invName|| ""  }}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">单位：</view>
					<view style="flex: 1;">{{ item.funitname || ""  }}</view>
				</view>
			</view>
			
			</u-checkbox-group>
			<view class="floatright">
				<u-button type="primary" @click="toAdd">确定</u-button>
			</view>
			<view class="loadmore">
				<u-loadmore :status="loadStatus"></u-loadmore>
			</view>
		</scroll-view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				multiple:true,
				value: [],
				radioValue:{},
				carType:'',
				address: [],
				provinceList: [],
				cityAllList: [],
				addressIndex: [0, 0],
				keywords: '',
				query: {
					pageNo: 1,
					pageSize: 5,
				},
				list: [],
				hid: '',
				fhList: [],
				addList: [],
				loadStatus: 'loadmore',
				show: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				index: '',
				confirmColor: { // 确定按钮颜色
					type: String,
					default: '#f9ae3d' // #f9ae3d
				},
				index: '',

			}
		},
		onLoad(params) {
			this.query.pageNo = 1;
			this.query.companyCode = params.companyCode;
			this.loadList();
			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.companySelectList = res;
			// });
		},
		methods: {
			checkboxChange(e){
				console.log(e,'e===')
			},
			addData(item,index) {
				console.log(index,'index===')
				item.checked = !item.checked
				// this.radioValue = item
			},
			toAdd() {
				// 判断checked是否为空，如果为空，就返回
				// 查找fhList中是否有勾选的数据，如果有，就返回选中的数据,否则，就返回提示
				// 获取选中的数据，并返回
				const checked = this.fhList.filter(item => item.checked);
				
				if (checked.length == 0) {
					this.$refs.jsError.showError('', "请选择至少一条数据！", 'error');
					return;
				}else{
					// chObj
					uni.$emit('spObjs', checked,this.index);
					uni.navigateBack({
						delta: 1,
					})	
				}

				// if (this.radioValue.cposName && this.radioValue.cposName != undefined) {
				// 	// chObj
				// 	uni.$emit('hwObjs', this.radioValue, this.index);
				// 	uni.navigateBack({
				// 		delta: 1,
				// 	})
				// } else {
				// 	this.$refs.jsError.showError('',"请选择至少一条数据！",'error');
				// 	return;
				// }
			},
			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadList("add");
				}, 100);
			},
			loadList(type) {
				this.$u.api.ktnw.basInvListData(this.query).then(res => {
					var data = res.list;
					data.forEach((item)=>{
						item.checked = false
					})
					if (type == "add") {
						for (var i = 0; i < data.length; i++) {
							this.fhList.push(data[i])
						}
					} else {
						this.fhList = data;
					}
					if (res.list.length >= res.count || res.list.length < 20) {
						this.loadStatus = "nomore";
					}
				})
			},
			startConfirm(e) {
				this.query.ddate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.ddate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			optionsClick(rowIndex, btnIndex) {
				let row = this.list[rowIndex];
				if (btnIndex == 0) {
					bpmUtils.navTrace(this, 'formKey=leave&bizKey=' + row.id);
				}
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			submit() {
				this.fhList = [];
				this.loadList();
				this.show = false
			},
			reset() {
				this.query = {
					pageNo: 1,
					pageSize: 5,
				};
				this.loadList();
				this.show = false
			},
		}
	}
</script>

<style  lang="less">
	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 15px;
		margin-right: 0;
		/* width: 100%; */
	
	}
	.tableclass {
		border-left: 1px solid rgb(228, 231, 237);
		border-top: 1px solid rgb(228, 231, 237);
		background-color: rgb(255, 255, 255);
		width: 100%;
		box-sizing: border-box;
	}

	.thclass {
		text-align: center;
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #303133;
		font-weight: bold;
		background-color: #f5f6f8;
	}

	.trclass {
		height: 52px;
	}

	.tdclass {
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #505256;
		align-self: stretch;
		box-sizing: border-box;
		height: 100%;
	}

	.box .item {
		margin: 0 0px 0px;
	}

	.tui-line-cell {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		align-items: center;
	}

	.tui-title {
		line-height: 32rpx;
		min-width: 120rpx;
		flex-shrink: 0;
	}

	.tui-input {
		font-size: 32rpx;
		color: #333;
		padding-left: 20rpx;
		flex: 1;
		overflow: visible;
	}

	/* .footer {
	position: fixed;
	left: 0;
	bottom: 20px;
	width: 100%;
	
} */
	.floatright {
		position: fixed;
		bottom: 12px;
		right: 12px;
		width: 25%;
		line-height: var(--footer-height);
		background: #ffffff;
		color: #ffffff;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}


	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 230rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
	
	
	.next-tree-check {
		width: 40px;
		height: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.next-tree-check-yes,
	.next-tree-check-no {
		width: 20px;
		height: 20px;
		border-top-left-radius: 20%;
		border-top-right-radius: 20%;
		border-bottom-right-radius: 20%;
		border-bottom-left-radius: 20%;
		border-top-width: 1rpx;
		border-left-width: 1rpx;
		border-bottom-width: 1rpx;
		border-right-width: 1rpx;
		border-style: solid;
		border-color: #f9ae3d;
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
	}

	.next-tree-check-yes-b {
		border-top-left-radius: 20%;
		border-top-right-radius: 20%;
		border-bottom-right-radius: 20%;
		border-bottom-left-radius: 20%;
		background-color: #f9ae3d;
		color: #fff;
		display: flex;
		justify-content: center;
		width: 100%;
	}

	.next-tree-check-yes-b .icon-text {
		font-size: 16px;
		font-weight: normal;
		font-family: uicon-iconfont;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.next-tree-check .radio {
		border-top-left-radius: 50%;
		border-top-right-radius: 50%;
		border-bottom-right-radius: 50%;
		border-bottom-left-radius: 50%;
	}

	.next-tree-check .radio .next-tree-check-yes-b {
		border-top-left-radius: 50%;
		border-top-right-radius: 50%;
		border-bottom-right-radius: 50%;
		border-bottom-left-radius: 50%;
	}
	
	
</style>