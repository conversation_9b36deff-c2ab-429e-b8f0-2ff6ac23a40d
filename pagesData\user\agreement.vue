<template>
  <div class="user-agreement">
    <h1>用户服务协议</h1>
    <p><strong>一、引言</strong></p>
    <p>欢迎使用本微信小程序！在使用本小程序之前，请您仔细阅读并同意遵守本用户服务协议（以下简称“本协议”）。本协议为您（以下简称“用户”）与本小程序的开发者（以下简称“开发者”）之间达成的协议，具有法律效力。</p>
    
    <p><strong>二、定义</strong></p>
    <ul>
      <li>“微信小程序”指本协议所述的由开发者开发并在微信平台上提供的小程序。</li>
      <li>“用户”指通过微信平台使用本小程序的个人或实体。</li>
      <li>“开发者”指开发并拥有本小程序的个人或实体。</li>
    </ul>
    
    <p><strong>三、用户同意</strong></p>
    <p>用户在使用本小程序之前，应仔细阅读本协议，并同意遵守本协议的所有条款和条件。如果用户不同意本协议的任何部分，请立即停止使用本小程序。</p>
    
    <p><strong>四、用户权利和义务</strong></p>
    <ul>
      <li>用户有权利使用本小程序以及其提供的功能和服务。</li>
      <li>用户应当遵守国家法律法规和相关规定，不得利用本小程序从事任何违法、违规或侵犯他人合法权益的行为。</li>
      <li>用户应当保护好自己的账号和密码安全，不得将其提供给他人使用。</li>
    </ul>
    
    <p><strong>五、开发者责任和义务</strong></p>
    <ul>
      <li>开发者应当依法合规开发、维护和运营本小程序，并提供稳定、安全的服务。</li>
      <li>开发者应当对用户的个人信息保密，并依法处理用户提供的个人信息。</li>
    </ul>
    
    <p><strong>六、免责条款</strong></p>
    <ul>
      <li>用户在使用本小程序过程中可能会遇到各种风险和问题，开发者不对此承担责任。</li>
      <li>开发者不对用户因使用本小程序而遭受的任何直接或间接损失承担责任。</li>
    </ul>
    
    <p><strong>七、协议的修改和终止</strong></p>
    <p>开发者有权随时修改本协议，并通过本小程序向用户公布。用户继续使用本小程序即表示同意接受修改后的协议。</p>
    
    <p><strong>八、法律适用和争议解决</strong></p>
    <ul>
      <li>本协议适用中华人民共和国法律。</li>
      <li>用户和开发者就本协议的解释和履行发生争议的，应通过友好协商解决；协商不成的，应提交至有管辖权的人民法院解决。</li>
    </ul>
    
    <p><strong>九、其他</strong></p>
    <ul>
      <li>本协议的标题仅为方便阅读而设，不影响其含义解释。</li>
      <li>如本协议中的任何条款无效或不可执行，不影响其他条款的效力。</li>
    </ul>
  </div>
</template>
 
<script>
export default {
  name: 'UserAgreement'
}
</script>
 
<style scoped>
.user-agreement {
  padding: 20px;
  font-size: 16px;
  line-height: 1.5;
}
h1, strong {
  font-size: 24px;
  margin-bottom: 20px;
}
ul {
  margin-left: 20px;
  padding-left: 20px;
  list-style-type: disc;
}
p {
  margin-bottom: 20px;
}
</style>