<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		
		
				<view class="cu-item shadow " style="margin-bottom: 10px;" >
					<view class="cu-form-group" >
						<view style="text-align: center;flex: 3;margin-right: 10px;font-weight: bold;">商品名称</view>
						<view style="flex: 1;text-align: center;font-weight: bold;">数量 </view>
					</view>
				</view>
				<view v-for="(item,index) in list"  class="cu-item shadow " style="margin-bottom: 10px;" :key="item.id">
					
					<view class="cu-form-group" >
						<view style="flex: 3;margin-right: 10px;">{{ item.fitemname || ''}}</view>
						<view style="flex: 1;text-align: center;">{{ item.fqty || "0" }} </view>
					</view>
					
				</view>

				
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
		
		<view class="footer" >
			<view style="border-bottom:1px solid #eee ;">
				<view class="cu-item shadow "  >
					<view class="cu-form-group" >
						<view style="text-align: center;flex: 3;margin-right: 10px;font-weight: bold;">本次上架合计</view>
						<view style="flex: 1;text-align: center;font-weight: bold;">{{sum}} </view>
					</view>
				</view>
			</view>
			<view style=" display: flex;justify-content: space-around;padding: 20rpx ;">
				<button class="cu-btn lines-default shadow-blur" @tap="cancel()">
					取消
				</button>
				<button style="margin: 0 10px;" class="cu-btn lines-green shadow-blur" @tap="submit()">
					确认
				</button>
				
			</view>
		</view>
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				sum:0,
				x: 650, //x坐标
				y: 650, //y坐标
				companySelectList:[],
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				show: false,
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				loadStatus: "loadmore",
				triggered: false,
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				djno:'',
				query:{
					// pageNo: 1,
					// pageSize: 5,
				}
			};
		},
		onShow() {
			this.$u.api.ktnw.companyTreeData().then(res => {
				this.companySelectList = res;
			});
			let pages = getCurrentPages();
			let currPage = pages[pages.length - 1];
			setTimeout(()=>{
				this.loadData();
			})
			this.focus = false;
			setTimeout(() => {
				this.focus = true;
			}, 500)

			
		},
		onLoad(e) {
			this.query['id'] = e.id
		},
		mounted() {
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
			handleFocus() {
				var _that = this;
				_that.focus = false;
				setTimeout(() => {
					_that.barCode = ''
					_that.focus = true;
				}, 500)
			},
			invCode(bar, companyCode) {
				let InventoryPrefix = this.vuex_config.InventoryPrefix;
				if (bar.indexOf(InventoryPrefix) != -1) {
					return bar
				} else {
					let code = `inv_${companyCode}_${bar}`
					return code
				}
			},
			confirm() {
				let _that = this;
				if (this.barCode) {
					let bar = encodeURIComponent(this.barCode)
					let InvBarType = _that.vuex_config.InvBarType;
					
					let barCode = this.invCode(bar, this.list[0].parent.companyCode)
					let data = {
						...this.query
					}
					this.$u.api.ktnw.getBarInfo({
						barCode: barCode,
						barType: InvBarType,
						companyCode: this.list[0].parent.companyCode
					}).then((res) => {
						if(res.result == 'true'){
							data.fitemno = res.data.basInv?.invCode
							// data.invName = res.data.basInv?.invName
							// _that.handleFocus()
							setTimeout(()=>{
								this.barCode = ''
							},500)
							_that.$u.api.ktnw.rds10ListData(data).then((res) => {
								if (res.list.length == 1) {
									_that.sendMp3('cg');
									uni.navigateTo({
										url: '/pages/ktnw/rksj/form?id=' + res.list[0].id
									})
								}else{
									_that.sendMp3('sb');
									_that.$refs.jsError.showError("", "请对应的商品条码！", "warn");
								}
							});
						}else{
							_that.handleFocus()
							_that.sendMp3('sb');
							let message = res.message
							_that.$refs.jsError.showError("", message, "error");
						}
						
					})
				} else {
					_that.handleFocus()
					// this.$u.toast("任务单号不能为空!");
					_that.$refs.jsError.showError("", "请扫描正常的商品条码", "warn");
				}
			},
			search() {
				let _that = this;
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			
		
			toForm(item){
				const that = this
				uni.navigateTo({
					url: '/pages/ktnw/rksj/form?id=' + item.id,
				})
			},
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			cancel(){
				uni.navigateBack({
					delta: 1
				})
			},
			submit() {
				this.$u.api.ktnw.rdconfirm({
					id: this.query.id,
				}).then(res => {
					if (res.result == 'true') {
						this.$u.toast(res.message);
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 500)
					} else {
						this.$refs.jsError.showError('', res.message, 'error');
					}
				});
			},
			startConfirm(e) {
				this.query.planArrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.planArrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},
			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.ktnw.findGenList(this.query).then((res) => {
					
					this.list = res.data.map(item=>{
						this.sum += item.fqty
						return item
					})
					
					
					
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 600rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;z-index: 999;
		border-top: 1px solid #eee;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>