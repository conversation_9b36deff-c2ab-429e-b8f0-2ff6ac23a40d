<template>
    <view class="feitui-select" @click.stop="trigger" :style="[feituiSelectSize]">
        <input type="text" v-model="value" :placeholder="placeholder" disabled clearable>
        <view class="feitui-select-suffix" :style="{border: '1px solid rgba（0,0,0,0)'}" :class="[showSuffix]">
            	<u-icon name="arrow-downward" size="20" color="#888888"></u-icon>
        </view>
        <view class="feitui-select-options" v-if="showOptions"
            :style="{'min-width': boundingClientRect.width + 'px', top: optionsGroupTop, margin: optionsGroupMargin}">
            <view class="feitui-select-options-item" v-for="(item,index) in options" :key="index"
                @click.stop="select(item)" :class="{active: currentSelect.cutomsName === item.cutomsName}">
                <text>{{item.cutomsName}}</text>
				<checkbox :checked="item.checked"></checkbox>
            </view>
        </view>
    </view>
</template>
<script>
    const COMPONENT_NAME = 'feitui-select'
    const MAX_OPTIONS_HEIGHT = 137 // 修改务必也修改feitui-select-options的css部分
    const OPTIONS_ITEM_HEIGHT = 38 // 修改务必也修改feitui-select-options-item的css部分
    const OPTIONS_MARGIN = 10
    const OPTIONS_PADDING = 6 * 2 + 2 // + 2是border
    const OPTIONS_OTHER_HEIGHT = OPTIONS_MARGIN + OPTIONS_PADDING
    const STORAGE_KEY = '_feituiWindowHeight'
    const SIZE = {
        'medium': {
            width: '240px',
            height: '40px'
        },
        'small': {
            width: '200px',
            height: '30px'
        },
        'mini': {
            width: '160px',
            height: '30px'
        }
    }

    export default {
        name: COMPONENT_NAME,
        props: {
            windowHeight: {
                type: [Number, String],
                default: 0
            },
            placeholder: {
                type: String,
                default: '请选择'
            },
            value: {
                type: String,
                default: ''
            },
            size: {
                type: String,
                default: 'medium'
            },
            options: {
                type: Array,
                default () {
                    return []
                }
            }
        },
        data() {
            return {
                showOptions: false,
                boundingClientRect: {},
                currentSelect: {},
                optionsGroupTop: 'auto',
                optionsGroupMargin: ''
            }
        },
        computed: {
            showSuffix() {
                return this.showOptions ? 'showOptions' : 'no-showOptions'
            },
            feituiSelectSize() {
                let size = this.size.toLowerCase()
                if (size in SIZE) {
                    return {
                        width: SIZE[size].width,
                        height: SIZE[size].height
                    }
                } else {
                    return {}
                }
            }
        },
        mounted() {
            const elQuery = uni.createSelectorQuery().in(this)
            elQuery.select('.feitui-select').boundingClientRect(data => {
                this.boundingClientRect = data
            }).exec();
            try {
                if (!this.windowHeight) {
                    const storageHeight = uni.getStorageSync(STORAGE_KEY)
                    if (storageHeight) {
                        this.feituiWindowHeight = storageHeight
                        return
                    }
                    const res = uni.getSystemInfoSync();
                    this.feituiWindowHeight = res.windowHeight
                    uni.setStorageSync(STORAGE_KEY, this.feituiWindowHeight)
                }
            } catch (e) {
                // error
            }
        },
        methods: {
            trigger(e) {
                const view = uni.createSelectorQuery().in(this)
                view.select('.feitui-select').fields({
                    rect: true
                }, data => {
                    let {
                        top,
                        bottom
                    } = data
                    const thresholdHeight = Math.min(MAX_OPTIONS_HEIGHT + OPTIONS_MARGIN, (this.options.length *
                            OPTIONS_ITEM_HEIGHT) +
                        OPTIONS_OTHER_HEIGHT)
                    bottom = Number(this.windowHeight || this.feituiWindowHeight) - (top + this
                        .boundingClientRect
                        .height) // 距离底部的距离等于视口的高度减上top加select组件的高度
                    // judge direction
                    if (bottom < thresholdHeight) {
                        this.optionsGroupDirection = 'up'
                        this.optionsGroupTop = -thresholdHeight - 12 + 'px'
                        this.optionsGroupMargin = '0'
                    } else {
                        this.optionsGroupDirection = 'down'
                        this.optionsGroupTop = 'auto'
                        this.optionsGroupMargin = '10px 0 0 0'
                    }
                    // if (this.scrollTop < )
                    this.showOptions = !this.showOptions
                }).exec();
            },
            select(options) {
                // this.showOptions = false
				options.checked = !options.checked
                this.currentSelect = options
				this.$forceUpdate()
                this.$emit('selectOne', options)
            },
            hideOptions() {
                this.showOptions = false
            }
        }
    }
</script>
<style scoped>
    .feitui-select {
        position: relative;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        /* font-size: 28rpx; */
        color: #606266;
        outline: none;
        box-sizing: content-box;
        height: 30px;
    }

    .feitui-select input {
        padding: 0 18rpx;
        padding-right: 60rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        height: 100% !important;
        min-height: 100% !important;
    }

    .feitui-select .feitui-select-suffix {
        position: absolute;
        box-sizing: border-box;
        height: 100%;
        right: 5px;
        top: 0;
        display: flex;
        align-items: center;
        transform: rotate(180deg);
        transition: all .3s;
        transform-origin: center;
    }

    .feitui-select .showOptions {
        transform: rotate(0) !important;
    }

    .feitui-select .no-showOptions {
        transform: rotate(180deg) !important;
    }

    .feitui-select .feitui-select-options {
        position: absolute;
        padding: 6px 0;
        margin-top: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        box-sizing: border-box;
        transform-origin: center top;
        z-index: 2238;
        overflow: scroll;
        max-height: 274rpx;
    }

    .feitui-select .feitui-select-options-item {
        padding: 0 20rpx;
        position: relative;
        white-space: nowrap;
        font-size: 14px;
        color: #606266;
        height: 40px;
        line-height: 40px;
        box-sizing: border-box;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
    }

    .feitui-select .active {
        background-color: #F5F7FA
    }
</style>
