<template>
	<view class="wrap">
		<view class="search">
			<u-search v-model="keywords" @custom="search" @search="search"></u-search>
		</view>
		<scroll-view class="scroll-list" scroll-y="true" @scrolltolower="loadMore">
			<u-cell-group class="list" :border="false">
				<u-swipe-action :options="options" v-for="(item, index) in list" :key="item.id" :index="index" @click="optionsClick">
					<u-cell-item :arrow="true" @click="optionsClick(index, 1)">
						<text slot="title">{{item.procIns.name || item.id}}</text>
						<text slot="label">{{item.assigneeInfo}} &nbsp;|&nbsp; 完成时间：{{item.endTime}}</text>
					</u-cell-item>
				</u-swipe-action>
			</u-cell-group>
			<view class="loadmore" @click="loadMore">
				<u-loadmore :status="loadStatus"></u-loadmore>
			</view>
		</scroll-view>
		<!-- <view class="btn-plus" @click="navTo('form')">
			<u-icon name="plus-circle-fill" size="90" color="#3d87ff"></u-icon>
		</view> -->
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
import bpmUtils from '@/pages/bpm/bpmUtils.js';
export default {
	data() {
		return {
			keywords: '',
			query: {
				status: 2, // 1 待办；2 已办
				pageNo: 1,
				pageSize: 20
			},
			list: [],
			count: 0,
			loadStatus: 'loadmore',
			options: [
				{text: '流程', style: { background: '#00aaff'}},
				{text: '表单', style: { background: '#dd524d'}}
			]
		};
	},
	onLoad() {
		this.loadList();
	},
	onShow() {
		if (uni.getStorageSync('refreshList') === true){
			uni.removeStorageSync('refreshList');
			this.search('');
		}
	},
	methods: {
		loadMore() {
			this.loadStatus = "loading";
			setTimeout(() => {
				this.query.pageNo += 1;
				this.loadList();
			}, 100);
		},
		loadList() {
			this.$u.api.bpm.myTaskList(this.query).then(res => {
				if (!res.list || res.list.length == 0){
					this.loadStatus = "nomore";
					return;
				}
				this.list = this.list.concat(res.list);
				this.count = res.count;
				this.query.pageNo = res.pageNo;
				this.query.pageSize = res.pageSize;
				this.loadStatus = "loadmore";
			});
		},
		optionsClick(rowIndex, btnIndex) {
			let row = this.list[rowIndex];
			if(btnIndex == 0) {
				bpmUtils.navTrace(this, 'id=' + row.procIns.id);
			}
			else if(btnIndex == 1) {
				this.$u.api.bpm.myTaskForm({id: row.id, status: this.query.status}).then(res => {
					bpmUtils.navForm(this, res);
				});
			}
		},
		search(value) {
			this.list = [];
			this.query.pageNo = 0;
			this.query['procIns.name'] = value;
			this.loadList();
		},
		navTo(url) {
			uni.navigateTo({
				url: url
			});
		}
	}
};
</script>
<style lang="scss">
page {
	background-color: #f8f8f8;
}
.btn-plus {
	position: absolute;
	bottom: 50rpx;
	right: 50rpx;
	z-index: 1;
	opacity: 0.6;
}
.btn-plus:hover {
	opacity: 1;
}
</style>
