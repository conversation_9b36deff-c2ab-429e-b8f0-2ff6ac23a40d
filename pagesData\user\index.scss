/*!
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * <AUTHOR>
 * @version 2020-9-1
 */
.header {
	background-color: #3E97B0;
	
	.userinfo {
		display: flex;
		padding: 40rpx;

		.image {
			flex-shrink: 0;
			width: 120rpx;
			height: 120rpx;
			image {
				border-radius: 100%;
				width: 100%;
				height: 100%;
			}
		}

		.info {
			display: flex;
			flex-flow: wrap;
			padding-left: 30rpx;
			color: #fff;

			.username {
				width: 100%;
				font-size: 40rpx;
				padding-top: 8rpx;
			}

			.usercode {
				height: 48rpx;
				line-height: 48rpx;
				padding: 0 20rpx;
				margin-top: 10rpx;
				background-color: rgba(0, 0, 0, 0.1);
				border-radius: 20rpx;
				font-size: 36rpx;
			}
		}
	}

	.logout {
		flex-shrink: 0;
		position: absolute;
		right: 70rpx;
		top: 65rpx;
		.u-btn {
			font-size: 30rpx;
		}
	}
}

.toolbar {
	padding: 0 4%;
	margin-bottom: 5rpx;
	border-radius: 0 0 100% 100%;
	background-color: #4094ff;

	.box {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		padding: 10rpx;
		border-radius: 15rpx;
		box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.15);
		background-color: #fefefe;

		.item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			// flex-flow: wrap;
			height: 120rpx;
			color: #666666;
			font-size: 30rpx;
			padding: 10rpx 10rpx;

			.icon {
				font-size: 50rpx;
			}

			.label {
				padding: 10rpx;
			}
		}

		.hover {
			background-color: #f6f6f6;
			border-radius: 15rpx;
		}
	}
}
