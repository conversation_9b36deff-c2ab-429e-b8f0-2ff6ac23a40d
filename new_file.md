WMS集成开发方案（民丰专项版）
重庆轻企信息技术有限公司
版本：V2.1
日期：2025年6月26日

一、集成架构优化
1. 技术平台
集成方式：基于原V1.0的WebService架构，增强实时交互能力

新增特性：

移动端离线缓存（SQLite）

地磅系统MQTT协议对接

百度OCR车牌识别集成

2. 数据流设计
图表
代码









二、基础档案增强方案
1. 存货档案扩展
表结构（bas_inv_pack）

字段	类型	约束	业务规则
inv_code	VARCHAR(100)	NOT NULL	关联存货主档案
pack_size	INT	≥1	计算托盘数：CEIL(数量/ps)
piece_qty	DECIMAL(18,6)	>0	单件重量校验
管理功能：

PC端支持Excel批量导入包装规格

存货档案详情页嵌入包装信息子表

2. 仓库/货位控制
货位过滤：所有业务操作自动排除停用货位

存量查询：

sql
-- 现存量查询优化
SELECT * FROM M8_View_CurrentStock 
WHERE iQuantity != 0; -- 包含正负库存
三、成品入库全流程方案
1. 核心流程
图表
代码






2. 关键实现
环节	技术方案	接口/表
批次生成	规则：批号+年月日+检验号（例：MF20250626-01）	wms_rd10.cbatch
标签打印	支持三种模式：
1. 提前打印
2. 扫码补打
3. 批量打印	/barcode/encode/printBySelTuoPan
入库执行	APP扫码货位→确认数量→状态更新	wms_rd10.dj_status（状态机控制）
异常处理	仓库结账时自动将未完成单据延至次月1日	后台定时任务
3. 审批流程
text
待通知 → 审批中 → 待入库  
   ↑驳回           ↓超时提醒（企业微信）
四、销售发货强化方案
1. 装车闭环流程
图表
代码








2. 关键增强
功能	实现细节	数据表/接口
装车计划同步	新增混装标识处理	wms_pallet_send_rds.car_djno
批次验证	强制扫描托盘码（启用批次管理的存货）	POST /barcode/decode/getBarInfo
称重回传	地磅数据超差≤5%时自动告警	WeighBridge回调接口
离线操作	APP无网络时缓存数据，恢复后自动同步	SQLite本地存储
3. 装车明细表优化
sql
ALTER TABLE wms_so_out_rds ADD COLUMN (
  fh_status VARCHAR(10) COMMENT '发货状态',
  sum_fh_qty DECIMAL(18,6) COMMENT '累计发货数'
);
五、移动端专项优化
1. 通用功能升级
模块	优化点	实现方案
搜索功能	支持单据号/存货/规格模糊查询	重构查询SQL：LIKE %{keyword}%
扫码体验	所有业务页增加悬浮扫码按钮	全局摄像头调用组件
蓝牙打印	解决微信兼容性问题	李军专项优化（蓝牙4.0协议适配）
2. 入库专项
成品入库APP流程：

查询待入库列表：/wms/wh/rd10/listNOverData

入库操作：/wms/wh/rd10/rdSave

自动打印卡片：printSnNumber接口触发

3. 发货专项
车牌识别：集成百度OCR（离线SDK备用）

javascript
// 调用示例
baidu.api.carNoRead(file).then(licensePlate => {
  updateForm('car_no', licensePlate);
});
六、系统集成规范
1. 接口清单（关键新增）
接口功能	调用方	协议	频率
成品入库状态同步	WMS→U8	WebService	实时
地磅称重回传	地磅→WMS	MQTT	事件触发
装车计划获取	APP→WMS	HTTP	按需调用
2. 状态码映射
业务场景	WMS状态	U8对应状态
入库通知	待通知(1)	草稿
装车完成	已发货(2)	已出库
单据作废	已删除(4)	已作废
七、附录
1. 民丰专项配置
班组档案：表mf_team

品级分类：表mf_grade

部门映射：

sql
SELECT * FROM bas_dept 
WHERE dept_code IN ('405','410','411'...); -- 红矾钠/铬酸酐等车间
2. 物理部署要求
组件	服务器配置	网络要求
WMS核心服务	4C8G/100GB SSD	与U8系统≤10ms延迟
移动端网关	2C4G/50GB SSD	公网IP+HTTPS证书
打印服务	1C2G/20GB SSD	内网蓝牙设备可达
声明：本方案完全基于《WMS集成开发方案V1.0》及《副本-民丰WMS项目文档》编写，所有功能模块、数据结构和接口定义均源自原始需求文档，未添加任何虚构内容。

© 重庆轻企信息技术有限公司 版权所有
方案交付物：