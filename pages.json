{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue",
		"tui-(.*)": "@/components/thorui/tui-$1/tui-$1.vue"
	},
	"pages": [{
			"path": "pages/sys/index/login/index",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/sys/list",
			"style": {
				"navigationBarTitleText": "功能业务",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/sys/searchList",
			"style": {
				"navigationBarTitleText": "查询",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "components/cjn-print/cjn-print",
			"style": {
				"navigationBarTitleText": "打印",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "components/cjn-print/cjn-print2",
			"style": {
				"navigationBarTitleText": "打印",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/jh/list",
			"style": {
				"navigationBarTitleText": "拣货单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/jh/list2",
			"style": {
				"navigationBarTitleText": "拣货明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/jh/form",
			"style": {
				"navigationBarTitleText": "拣货单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pd/list",
			"style": {
				"navigationBarTitleText": "盘点单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pd/list2",
			"style": {
				"navigationBarTitleText": "盘点明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pd/form",
			"style": {
				"navigationBarTitleText": "盘点单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pdPos/list",
			"style": {
				"navigationBarTitleText": "仓库盘点",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pdPos/list2",
			"style": {
				"navigationBarTitleText": "仓库盘点明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pdPos/list3",
			"style": {
				"navigationBarTitleText": "仓库盘点明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/pdPos/form",
			"style": {
				"navigationBarTitleText": "仓库盘点单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/rksj/list",
			"style": {
				"navigationBarTitleText": "入库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/rksj/qrlist",
			"style": {
				"navigationBarTitleText": "本次上架",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/rksj/list2",
			"style": {
				"navigationBarTitleText": "入库明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtrksj/list2",
			"style": {
				"navigationBarTitleText": "其他上架-入库明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/tlxj/list2",
			"style": {
				"navigationBarTitleText": "出库明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtrkxj/list",
			"style": {
				"navigationBarTitleText": "其他下架-出库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtrkxj/list2",
			"style": {
				"navigationBarTitleText": "其他下架-出库明细",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/rksj/form",
			"style": {
				"navigationBarTitleText": "入库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtrksj/form",
			"style": {
				"navigationBarTitleText": "其他上架-入库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/tlxj/form",
			"style": {
				"navigationBarTitleText": "退料下架-出库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtrkxj/form",
			"style": {
				"navigationBarTitleText": "其他下架-出库单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		// 
		{
			"path": "pages/ktnw/spzl/index",
			"style": {
				"navigationBarTitleText": "商品重量录入",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/ckdd/list",
			"style": {
				"navigationBarTitleText": "出库订单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/ckdd/list1",
			"style": {
				"navigationBarTitleText": "拣货单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/ckdd/list2",
			"style": {
				"navigationBarTitleText": "出库订单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/ckdd/form",
			"style": {
				"navigationBarTitleText": "出库订单",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/xsth/list",
			"style": {
				"navigationBarTitleText": "销售退货",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/xsth/form",
			"style": {
				"navigationBarTitleText": "销售退货",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/xsth/index",
			"style": {
				"navigationBarTitleText": "退货上架",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/jhH/list",
			"style": {
				"navigationBarTitleText": "拣货记录",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/jhH/form",
			"style": {
				"navigationBarTitleText": "拣货记录",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtsj/index",
			"style": {
				"navigationBarTitleText": "上架",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtsj/list",
			"style": {
				"navigationBarTitleText": "上架记录",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtxj/index",
			"style": {
				"navigationBarTitleText": "下架",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtxj/list",
			"style": {
				"navigationBarTitleText": "下架记录",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/hwtz/index",
			"style": {
				"navigationBarTitleText": "库内转移",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtsj/xzspList",
			"style": {
				"navigationBarTitleText": "选择商品",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/xsth/xzspList",
			"style": {
				"navigationBarTitleText": "选择商品",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ktnw/qtsj/hwXz",
			"style": {
				"navigationBarTitleText": "货位选择",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},

		/* 用户 */
		{
			"path": "pages/sys/index/user/index",
			"style": {
				"navigationBarTitleText": "用户中心",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},

		{
			"path": "uview-ui/components/u-avatar-cropper/u-avatar-cropper",
			"style": {
				"navigationBarTitleText": "头像裁剪",
				"navigationBarBackgroundColor": "#000000"
			}
		},
		{
			"path": "uview-ui/components/u-avatar-cropper/u-avatar-card",
			"style": {
				"navigationBarTitleText": "车牌裁剪",
				"navigationBarBackgroundColor": "#000000"
			}
		},
		// 
		{
			"path": "pages/lq-upgrade/upgrade",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none",
					"animationType": "none", //取消窗口动画
					"background": "transparent" // 设置背景透明
				}
			}
		},
		/* 工作流引擎 */
		{
			"path": "pages/bpm/myTaskTodo",
			"style": {
				"navigationBarTitleText": "待办任务",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/bpm/myTaskHistory",
			"style": {
				"navigationBarTitleText": "已办任务"
			}
		},
		{
			"path": "pages/bpm/myRuntime",
			"style": {
				"navigationBarTitleText": "我相关的"
			}
		},
		/* Common */
		{
			"path": "pages/common/webview",
			"style": {
				"navigationBarTitleText": "浏览网页"
			}
		}

	],
	"subPackages": [{
	    "root": "pagesData",  //文件夹名称
	    "pages": [
	        {
	            "path" : "data/stockListData",
				"style": {
					"navigationBarTitleText": "现存量查询",
					"navigationBarBackgroundColor": "#3E97B0",
					"navigationBarTextStyle": "white"
				}
	        },
			{
			    "path" : "data/posSumListData",
				"style": {
					"navigationBarTitleText": "货位存量查询",
					"navigationBarBackgroundColor": "#3E97B0",
					"navigationBarTextStyle": "white"
				}
			},
			{
			    "path" : "data/posListData",
				"style": {
					"navigationBarTitleText": "货位实仓查询",
					"navigationBarBackgroundColor": "#3E97B0",
					"navigationBarTextStyle": "white"
				}
			},
			{
				"path": "user/info",
				"style": {
					"navigationBarTitleText": "个人信息"
				}
			},{
			"path": "user/pwd",
			"style": {
				"navigationBarTitleText": "修改密码",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "user/about",
			"style": {
				"navigationBarTitleText": "关于我们",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "user/print",
			"style": {
				"navigationBarTitleText": "蓝牙打印机设置",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "user/agreement",
			"style": {
				"navigationBarTitleText": "用户服务协议",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "user/privacy",
			"style": {
				"navigationBarTitleText": "隐私政策",
				"navigationBarBackgroundColor": "#3E97B0",
				"navigationBarTextStyle": "white"
			}
		}
	    ]
		
	}],
	"preloadRule": {
		"pages/sys/list": {
			"network": "all",
			"packages": ["pagesData"]
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "BTDM",
		"navigationBarBackgroundColor": "#f8f8f8"
	}
	,
	"tabBar": {
		"color": "#909399",
		"selectedColor": "#1296db",
		"backgroundColor": "#ffffff",
		"height": "64px",
		"borderStyle": "white",
		"position": "bottom",
		"iconWidth": "30px",
		"fontSize":"18px",
		"spacing":"4px",
		"list": [
			{
				"pagePath": "pages/sys/searchList",
				"iconPath": "static/image/zfgs/xm.png",
				"selectedIconPath": "static/image/zfgs/xm_select.png",
				"text": "查询"
			},
			{
				"pagePath": "pages/sys/list",
				"iconPath": "static/image/zfgs/xm.png",
				"selectedIconPath": "static/image/zfgs/xm_select.png",
				"text": "业务功能"
			},
			{
				"pagePath": "pages/sys/index/user/index",
				"iconPath": "static/image/zfgs/my.png",
				"selectedIconPath": "static/image/zfgs/my_select.png",
				"text": "我的"
			}
		]
	},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}

}