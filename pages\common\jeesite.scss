/*!
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * <AUTHOR>
 * @version 2020-9-1
 */
.u-btn {
	&--info {
		color: #ffffff;
		border-color: #01abb4;
		background-color: #01abb4;
	}
	&--purple {
		color: #ffffff;
		border-color: #5d5fe7;
		background-color: #5d5fe7;
	}
}

.u-cell-box {
	
	.u-cell {
		font-size: 30rpx;
	}
	
	.u-cell_title {
		font-size: 34rpx;
	}
	
	.u-cell__left-icon-wrap {
		margin-right: 18rpx;
	}
}

.search{
	padding: 20rpx 20rpx 0;
	background: #f8f8f8;
	height: 105rpx;
}

.scroll-list {
	height: calc(100vh - var(--window-top) - var(--window-bottom) - 105rpx); // 105rpx 为 .search 的高度
	width: 100%;

	.loadmore {
		padding: 20rpx;
	}
}

.box {
	padding-bottom: 10rpx;
	
	.item {
		margin: 0 20rpx 20rpx;
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
		box-sizing: border-box;
		background-color: #fff;
		font-size: 28rpx;
		
		.title {
			display: flex;
			justify-content: space-between;
			background-color: #fff;
			padding-left: 15rpx;
			align-items: center;
			
			.text {
				margin: 0 20rpx;
				font-size: 35rpx;
				font-weight: bold;
			}
			
		}
	}
	
}

.list {
	
	.u-cell-item-box {
		
		.u-swipe-content {
			width: 750rpx;
		}
		
		.u-cell_title {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			min-width: 655rpx;
		}
		
		.u-border-bottom:after {
			border-bottom-width: 0;
		}
		
	}
	
}

.form {
	display: flex;
	flex-direction: column;
	padding: 20rpx 30rpx;
	
	.u-cell-item-box {
		border-radius: 20rpx;
	}
}
	
.form-footer {
	display: flex;
	margin: 10rpx;
	padding-bottom: 30rpx;
	
	.btn {
		flex: 1;
		margin: 20rpx;
		margin-bottom: 10rpx;
	}
}
