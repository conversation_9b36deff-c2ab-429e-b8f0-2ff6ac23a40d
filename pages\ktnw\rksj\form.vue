<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<!-- <u-sticky class="u-sticky">
			<view class="cu-bar search" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
				@search="smconfirm"></u-search>
				
			</view>
		</u-sticky> -->
		<u-form class="form bg-white" :model="model" ref="uForm" label-position="left">
			<u-form-item label="单据号:" prop="fbillno" label-width="200">
				<u-input placeholder=" " v-model="model.parent.fbillno" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="公司:" prop="fbillno" label-width="200">
				<u-input placeholder=" " v-model="model.parent.companyName" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="商品编码:" prop="viewCode" label-width="200">
				<u-input placeholder=" " v-model="model.basInv.viewCode" type="text" :disabled="true" di
					maxlength="64"></u-input>
				<!-- position: fixed;top: 80rpx;left: 20rpx; -->
				<view v-if="invFlag" @click="handleFocus" style="text-align: center;margin: 0 20rpx;">
					<u-icon name="checkmark-circle" color="#81b337" size="60"></u-icon>
					<view style="color: #81b337"> 验证通过 </view>
				</view>
				<view v-if="!invFlag" @click="handleFocus" style="text-align: center;left: 20rpx;margin: 0 20rpx;">
					<u-icon name="info-circle" color="#ff9900" size="60"></u-icon>
					<view style="color: #ff9900"> 暂未验证 </view>
				</view>
			</u-form-item>
			<u-form-item label="商品名:" prop="fitemname" label-width="200">
				<u-input placeholder=" " v-model="model.basInv.invName" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="形象刊:" prop="cfree1" label-width="200">
				<u-input placeholder=" " v-model="model.freeVO.cfree1" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="仓库:" prop="fstockname" label-width="200">
				<u-input placeholder=" " v-model="model.fstockname" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>

			<view style="display: flex;">
				<u-form-item label="应收:" prop="fqty" label-width="100">
					<u-input placeholder=" " v-model="model.fqty" type="text" :disabled="true" di
						maxlength="64"></u-input>
				</u-form-item>
				<u-form-item label="实收:" prop="sumRkQty" label-width="100" style="margin: 0 10px;">
					<u-input placeholder=" " v-model="model.sumRkQty" type="text" :disabled="true" di
						maxlength="64"></u-input>
				</u-form-item>
				<u-form-item label="剩余:" prop="syQty" label-width="100">
					<u-input placeholder=" " v-model="model.syQty" type="text" :disabled="true" di
						maxlength="64"></u-input>
				</u-form-item>
			</view>

		</u-form>

		<view class="action bg-white "
			style="color: #3E97B0;font-size: 20px;align-items: center;display: flex;padding: 0 0 10px 10px;">
			<text class="cuIcon-titles text-bold">入库货位明细</text>
		</view>
		<view style="padding: 10px;">
			<view v-for="(item,index) in children" :id="'id'+ item.id" class="cu-item shadow " style="position: relative;" :key="index">				
				<view class="cu-form-group"
					style="display: flex;justify-content: space-between;">
					<view
						style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
						{{ index + 1 }}
					</view>
					<view >
						<text 
							style="font-size: 35px;" class="cuIcon-deletefill text-sl text-red"
							@tap="delDetail(item,index)"></text>
					</view>
				</view>
				
				<view class="cu-form-group">
					<view class="title">入库货位：</view>
					<view style="flex: 1;"> {{ item.cposName || ""  }} {{ item.cposCode ? '('+ item.cposCode +')' :''  }}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">已入库数：</view>
					<view style="flex: 1;"> {{ item.fqty|| "0"  }} </view>
					<!-- <button class="cu-btn  bg-confirm lg  " @click="thClick(item)" v-if="item.sumQty && model.jhStatus!=0">退回</button> -->
				</view>
			</view>
	</view>

		<u-modal v-model="thshow" title="退回" @confirm="thconfirm" :show-cancel-button="true" width="80%"
			@cancel="thCancel">
			<!-- <view class="slot-content">
			<view class="cu-bar" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请扫描ASN单号"
					:show-action="false" @search="confirm"></u-search>
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="search" name="scan" size="50"></u-icon>
				</view>
			</view>
		</view> -->
			<u-form class="form bg-white" :model="model" ref="uForm" label-position="left" style="padding: 0 10px;">
				<view style="display: flex;justify-content: space-between;">
					<view style="flex: 1;">
						<u-form-item label="退回货位:" prop="posName" label-width="180"
							:label-style="{ 'font-weight': 'bold' ,'color':'red'}">
							<u-search search-icon='' v-model="model.posName" ref="uSearch" placeholder="请先聚焦后扫描"
								:clearabled="false" :show-action="false" @search="smconfirm('PosName')" bgColor="#fff"
								:focus="thFocus"></u-search>
							<u-icon @click="xzhw" name="arrow-right" size="70"></u-icon>
						</u-form-item>
					</view>
				</view>
				<u-form-item label="退回数量:" prop="thIqty" label-width="180"
					:label-style="{ 'font-weight': 'bold','color':'red' }">
					<u-input v-model="model.thIqty" type="number" placeholder="请输入" clearable />
				</u-form-item>
			</u-form>
		</u-modal>

		<u-modal v-model="jhshow" title="本次入库" @confirm="tjconfirm" :show-cancel-button="true" width="80%"  :negative-top="570"
			@cancel="cancel">
			<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
				<u-form-item label="商品:" prop="cposCode" label-width="100">
					{{ model.basInv.invName }}
				</u-form-item>
			</u-form>
			<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
				<u-form-item label="货位:" prop="cposCode" label-width="100">
					{{ jhData.cposName }}
				</u-form-item>
			</u-form>
			<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
				<u-form-item label="剩余数量:" prop="cposCode" label-width="180">
					{{ jhData.syQty || 0 }}
				</u-form-item>
			</u-form>
			<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
				<u-form-item label="入库数量:" prop="iqty" label-width="180"
					:label-style="{ 'font-weight': 'bold','color':'red' }">
					<!-- type="digit" :focus="jhQtyFocus" -->
					<u-input v-model="jhData.jhQty" :focus="jhQtyFocus" type="number" placeholder="请输入" @input="replaceInput" clearable />
				</u-form-item>
			</u-form>
		</u-modal>

		<view>
		<movable-area class="movable-area" v-if="model.jhStatus!=2">
			<movable-view class="movable-view" :x="x" :y="y" direction="all">
				<u-button size="mini" @click="submit" type="primary"
					style="width: 90px; height: 70px; color: #fff; font-size: 12px">本次上架完成
				</u-button>
			</movable-view>
		</movable-area>
	</view>
		<!-- <view>
			<movable-area class="movable-area1">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="handleFocus" type="success"
						style="width: 90px; height: 70px; color: #fff; ">
						<view style="font-size: 16px;display: flex; flex-direction: column;text-align: center;">
							<u-icon style="margin: auto;" name="/static/jeesite/sm1.png" size="80"></u-icon>
							<view>扫一扫</view>
						</view>
					</u-button>

				</movable-view>
			</movable-area>
		</view> -->
	</view>
</template>

<script>
	import config from '@/common/config.js';
	export default {
		data() {
			return {
				jhData: {},
				thshow: false,
				jhshow: false,
				barCode: "",
				jhQtyFocus: false,
				focus: false,
				shQty: '',
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				invFlag: false,
				model: {
					parent: {},
					basInv: {

					},
					freeVO: {

					},
				},
				//主表id
				query: {
					id: '',
				},
				//子表id
				querys: {
					id: '',
				},
				children: [],
				carVenSelectList: [],
				pickerTime: false, //控制日期显示
				currentCposCode: {},
				currentThCposCode: {},
				x: 650, //x坐标
				y: 650, //y坐标
				thFocus: false,
				paramsId: '',
				rdrecords10PosList:[],
				// 防止二次提交标志
				thSubmitting: false, // 退回提交中
				tjSubmitting: false  // 入库提交中
			}
		},
		onLoad(params) {
			if (params.id) {
				this.paramsId = params.id
				this.loadList({
					id: params.id
				});
			}
			uni.$on('hwObjs', (arr) => {
				if (arr.length) {
					this.model.posCode = arr[0].id
					this.model.posName = arr[0].name
					this.$forceUpdate()
				}
			})
		},
		onShow() {
			// console.log('页面开启（onLoad）广播监听事件：xwscan')
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
			this.rdrecords10PosList = []
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			handleFocus2(e) {
			    // 方法 2: 使用 uni.hideKeyboard() 确保键盘隐藏
			    setTimeout(()=>{
					uni.hideKeyboard()
				},100)
			},
			delDetail(item,index){
				console.log('item',item)
				let _that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
						_that.children.splice(index,1)
						_that.rdrecords10PosList.splice(index,1)
					}
				})
				
			},
			handleFocus() {
				console.log('focus', this.focus)
				this.focus = false;
				setTimeout(() => {
					this.focus = true;
				}, 500)
				console.log('focus', this.focus)
			},
			thClick(item) {
				this.thshow = true
				this.currentThCposCode = item,
					this.model.posName = item.cposName
				this.model.posCode = item.cposCode
				this.thFocus = false
				setTimeout(() => {
					this.thFocus = true
				})
			},
			thCancel() {
				console.log('thCancle')
				this.thshow = false
				this.currentCposCode = {}
				this.model.posName = ''
				this.model.posCode = ''
				this.model.thIqty = ''
				this.thFocus = false
				// 重置提交状态
				this.thSubmitting = false
				this.handleFocus()
			},
			changeIqty(item, index) {
				console.log(item, 'item====');
				// this.model.iqty = item.arrQty
				this.jhshow = true
				this.currentCposCode = {
					cposCode: item.cposCode,
					cposName: item.cposName,
				}
			},
			cancel() {
				this.jhshow = false
				this.currentCposCode = {}
				this.model.iqty = ''
				// 重置提交状态
				this.tjSubmitting = false
				this.handleFocus()
			},
			thconfirm() {
				var _that = this;

				// 防止二次提交
				if(_that.thSubmitting) {
					_that.$u.toast('正在退回中，请稍候...');
					return;
				}

				const findItem = _that.children.find(item => item.id == _that.currentThCposCode.id)
				if (_that.model.thIqty > _that.model.sumJhQty) {
					let message = '退回数量不能大于已拣货数量'
					this.thshow = true
					_that.$refs.jsError.showError("", message, "error");
					return;
				}

				// 设置提交状态
				_that.thSubmitting = true;

				// 显示加载提示
				uni.showLoading({
					title: '退回中...',
					mask: true
				});

				const findItemList = [{
					...findItem,
					jhQty: _that.model.thIqty,
					cposCode: _that.model.posCode,
					cposName: _that.model.posName
				}]
				_that.$u.api.ktnw.jhCallBack({
					id: _that.model.id,
					jhPickDetailsPosList: findItemList,
				}).then(res => {
					// 隐藏加载提示
					uni.hideLoading();

					if (res.result == 'true') {
						_that.currentThCposCode = {}
						_that.model.posName = ''
						_that.model.posName = ''
						_that.model.thIqty = ''
						this.loadList({
							id: this.paramsId
						});
						this.handleFocus()
					} else {
						_that.$refs.jsError.showError("", res.message, "error");
					}
					// console.log()
				}).catch(err => {
					// 隐藏加载提示
					uni.hideLoading();
					// 处理请求错误
					console.error('退回失败:', err);
					_that.$refs.jsError.showError("", "网络请求失败，请检查网络连接后重试", "error");
				}).finally(() => {
					// 重置提交状态
					_that.thSubmitting = false;
				});
			},
			tjconfirm() {
				var _that = this;

				// 防止二次提交
				if(_that.tjSubmitting) {
					_that.$u.toast('正在处理中，请稍候...');
					return;
				}

				// if (_that.jhData.jhQty > _that.jhData.syQty) {
				// 	let message = '拣货数量不能大于剩余拣货数量'
				// 	_that.$refs.jsError.showError("", message, "error");
				// 	return;
				// }
				if (!_that.jhData.jhQty || _that.jhData.jhQty == 0) {
					let message = '入库数量不能为零'
					_that.$refs.jsError.showError("", message, "error");
					return;
				}

				// 设置提交状态
				_that.tjSubmitting = true;

				try {
					// 显示加载提示
					uni.showLoading({
						title: '处理中...',
						mask: true
					});

					this.rdrecords10PosList.push({
						hid: _that.jhData.id,
						cposCode:_that.jhData.cposCode,
						cposName:_that.jhData.cposName,
						fqty:_that.jhData.jhQty,
					})

					this.children = this.rdrecords10PosList

					// 隐藏加载提示
					uni.hideLoading();
					_that.$u.toast('添加成功');

					// 关闭弹窗并清空数据
					this.jhshow = false;
					_that.jhData = {};

				} catch (err) {
					// 隐藏加载提示
					uni.hideLoading();
					console.error('处理失败:', err);
					_that.$refs.jsError.showError("", "操作失败，请重试", "error");
				} finally {
					// 重置提交状态
					_that.tjSubmitting = false;
				}
				// this.$u.api.ktnw.rdposUpSave({
				// 	hid: _that.jhData.id,
				// 	cposCode:_that.jhData.cposCode,
				// 	cposName:_that.jhData.cposName,
				// 	fqty:_that.jhData.jhQty,
					
				// }).then(res => {
				// 	if (res.result == 'true') {
				// 		_that.sendMp3('cg');
				// 		this.$u.toast(res.message);
				// 		_that.currentThCposCode = {}
				// 		// _that.model.posName = ''
				// 		// _that.model.posName = ''
				// 		// _that.model.thIqty = ''
				// 		_that.model = {
				// 			parent: {},
				// 			basInv: {
							
				// 			},
				// 			freeVO: {
							
				// 			},
				// 		}
				// 		_that.jhData = {}
				// 		this.loadList({id:this.paramsId});
				// 		this.jhshow = false
				// 		this.handleFocus()
				// 		// setTimeout(() => {
				// 		// 	uni.navigateBack({
				// 		// 		delta: 1
				// 		// 	})
				// 		// }, 500)
				// 	} else {
				// 		_that.sendMp3('sb');
				// 		this.$refs.jsError.showError('', res.message, 'error');
				// 	}
				// });

			},
			tjconfirm2() {
				var _that = this;

				const findPosCode = _that.children.findIndex(item => item.cposCode == _that.currentCposCode.cposCode)
				if (findPosCode != -1) {
					if (_that.model.iqty > _that.model.syQty) {
						let message = '拣货数量不能大于剩余拣货数量'
						this.jhshow = true
						_that.$refs.jsError.showError("", message, "error");
					} else {
						_that.children[findPosCode].jhQty = _that.model.iqty
						_that.children[findPosCode].yzPos = true
						_that.$forceUpdate()
						// 计算意见数量和剩余数量
						_that.model.sumJhQty = _that.children.reduce((total, item) => {
							// console.log(item.arrQty,'item.arrQty')	
							return total + Number(item.jhQty) || 0;
						}, this.model.sumJhQty || 0)

						_that.model.syQty = Number(_that.model.fqty) - Number(_that.model.sumJhQty);
						// console.log(_that.model.syQty,'model.syQty',yjQty,_that.model.yjQty)

						_that.$forceUpdate()


						// _that.model.syQty = Number(_that.model.fqty) || 0 - Number(_that.model.cqty) || 0;

						uni.pageScrollTo({
							selector: '#id' + _that.children[findPosCode].id,
							duration: 300
						})
						this.handleFocus()
						setTimeout(() => {
							_that.currentCposCode = {}
							this.model.iqty = ''
						})
					}

				}
			},
			replaceInput(e) {
				console.log(e);
				var that = this
				// e = e.match(/^\d*(\.?\d{0,2})/g)[0]
				// this.$nextTick(() => {
				// 	that.model.fqty = e
				// })

			},
			invCode(bar, companyCode) {
				let InventoryPrefix = this.vuex_config.InventoryPrefix;
				if (bar.indexOf(InventoryPrefix) != -1) {
					return bar
				} else {
					let code = `inv_${companyCode}_${bar}`
					return code
				}
			},
			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					this.smconfirm()
					//将换行符分割成数组
					// const allItems = newString.split('\n');
					// 	//遍历数组，将每一项添加到arr中
					// 	for(let i = 0;i<allItems.length;i++){
					// 		this.arr.push({
					// 			"content":allItems[i],
					// 			"remarks":this.remarks
					// 		})
					// 	}
				}
			},
			smconfirm(PosName) {
				let _that = this;
				_that.focus = false
				let InventoryPrefix = _that.vuex_config.InventoryPrefix;
				let PositionPrefix = _that.vuex_config.PositionPrefix;
				let InvBarType = _that.vuex_config.InvBarType;
				let PosBarType = _that.vuex_config.PosBarType;
				let bar = ''
				if (PosName == 'PosName') {
					bar = encodeURIComponent(this.model.posName)
					this.model.posName = ''
				} else {
					bar = encodeURIComponent(this.barCode)
				}
				console.log('bar', bar)

				if (bar.indexOf(PositionPrefix) != -1) {
					this.$u.api.ktnw.getBarInfo({
						barCode: bar,
						barType: PosBarType,
						// whCode: _that.model.fstockno || ''
						whCode: _that.model.fstockid || ''
					}).then((res) => {
						if (res.result == 'true') {
							if(res.data?.basPos?.treeLeaf == '0') {
								this.$forceUpdate()
								_that.sendMp3('sb');
								let message = '请扫描末级货位'
								_that.$refs.jsError.showError("", message, "error");
								return;
							}							
							if (PosName == 'PosName') {
								_that.model.posCode = res.data.bizKey
								_that.model.posName = res.data.name
								setTimeout(() => {
									_that.barCode = ''
									_that.focus = true;
								}, 500)
							} else {
								_that.sendMp3('cg');
								setTimeout(() => {
									_that.barCode = ''
									// _that.focus = true;
								}, 500)
								_that.jhData = {
									..._that.model,
									cposCode:  res.data.code,
									cposName: res.data.name,
								}
								_that.currentCposCode = {
									cposCode:  res.data.code,
									cposName: res.data.name,
								}
								_that.jhshow = true
								
								_that.jhQtyFocus = false
								setTimeout(() => {
									_that.jhQtyFocus = true;
								}, 500)

								_that.$forceUpdate()
							}



						} else {
							setTimeout(() => {
								_that.barCode = ''
								_that.focus = true;
							}, 500)
							_that.sendMp3('sb');
							let message = res.message
							_that.$refs.jsError.showError("", message, "error");

						}
					})
				} else {

					let barCode = this.invCode(bar, this.model.parent.companyCode)
					this.$u.api.ktnw.getBarInfo({
						barCode: barCode,
						barType: InvBarType,
						companyCode: this.model.parent.companyCode
					}).then((res) => {
						if (res.result == 'true') {
							if (res.data.basInv && this.model.basInv.invCode == res.data.basInv.invCode) {
								_that.sendMp3('cg');
								setTimeout(() => {
									_that.barCode = ''
									_that.focus = true;
								}, 500)
								_that.invFlag = true
								_that.$forceUpdate()
							} else {
								setTimeout(() => {
									_that.barCode = ''
									_that.focus = true;
								}, 500)
								_that.sendMp3('sb');
								let message = '请扫描对应的商品码'
								_that.$refs.jsError.showError("", message, "error");
							}

						} else {
							setTimeout(() => {
								_that.barCode = ''
								_that.focus = true;
							}, 500)
							_that.sendMp3('sb');
							let message = res.message
							_that.$refs.jsError.showError("", message, "error");

						}

					})


					// setTimeout(() => {
					// 	_that.barCode = ''
					// 	_that.focus = true;
					// }, 500)
					// _that.sendMp3('bcz');
					// _that.$refs.jsError.showError("", "请扫描正确的商品码或货位码", "error");

				}



			},
			xzhw() {
				let whcode = this.model.whcode || ''
				uni.navigateTo({
					url: "/pages/ktnw/qtsj/hwXz?whcode=" + whcode,
				});
			},
			paste() {
				let that = this
				uni.getClipboardData({
					success: (res) => { // 获取成功回调
						that.model.carNo = res.data.trim().slice(0, 8)
						that.$forceUpdate()
						if (!that.model.cdriver && !that.model.driverPhone) {
							that.$u.api.mffh.getCarInfoByCarNo({
								carNo: that.model.carNo
							}).then(res => {
								that.model.cdriver = res.data.cdriver || '';
								that.model.driverPhone = res.data.driverPhone || '';
								that.$forceUpdate()
							});
						}
					}
				})
			},
			loadList(data) {
				this.$u.api.ktnw.rds10Form(data).then(res => {
					// console.log(res,'res')
					if (res.result == 'true') {

						this.model = {
							...this.model,
							...res.data,
						}
						const syQty = (Number(this.model.fqty) || 0) - (Number(this.model.sumRkQty) || 0)
						this.model.syQty = syQty
						this.children = res.data.rdrecords10PosList || []
						// this.children = this.children.map(item => {
						// 	return {
						// 		...item,
						// 		jhQty:'',
						// 		yzPos:false,
						// 	}
						// })
					}
				})
			},

			submit() {
				const rkTotalNumber = this.rdrecords10PosList.reduce((total, item) => {
					return total + Number(item.fqty) || 0;
				}, 0)
				
				

				if (rkTotalNumber > this.model.syQty) {
					
					const ccNumber =  Number(rkTotalNumber) - Number(this.model.syQty)
					uni.showModal({
						title: '数量验证',
						content: '本次入库超出应入库'+ccNumber+'册',
						confirmColor: '#F54E40',
						success: (res) => {
							if (!res.confirm) {
								return false;
							}
							this.getRdconfirm();
						}
					})
				} else {
					this.getRdconfirm();
				}
			},

			getRdconfirm() {
				this.$u.api.ktnw.rdconfirm({
					cid: this.model.cid,
					rdrecords10PosList: this.rdrecords10PosList
				}).then(res => {
					if (res.result == 'true') {
						this.$u.toast(res.message);
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 500)
					} else {
						this.$refs.jsError.showError('', res.message, 'error');
					}
				});
			},
			sendMp3(name) {
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
		}
	}
</script>
<style lang="scss" scoped>
	
	/* .footer {
	position: fixed;
	bottom: 0;
	right:0;
	width: 100%;
	line-height: var(--footer-height);
	background: #ffffff;
	color: #ffffff;
} */
	$all_width: 96rpx;
	$all_height: 96rpx;

	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	
	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.btn-plus {
		position: fixed;
		bottom: 260rpx;
		right: 40rpx;
		z-index: 2;
		opacity: 0.6;
	}

	.btn-plus:hover {
		opacity: 1;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */

	}

	.cu-bar {
		min-height: 80px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 200rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}


	.cu-modal-footer {
		padding: 32rpx 32rpx !important;
		width: 100%;

		.cu-btn {
			width: 50%;
		}

	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;
		z-index: 999;
		border-top: 1px solid #eee;
	}

	/deep/ .u-model__title[data-v-3626fcec] {
		color: #fff !important;
		padding: 10px !important;
		background: #3e97b0 !important;
		font-weight: bold !important;
	}
</style>