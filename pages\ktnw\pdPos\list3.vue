<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<view class="cu-bar search" style="padding: 10px" @tap="show=true">
			<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描商品" :show-action="false"
				@tap="show=true" :disabled="true"></u-search>
			
		</view>
		<u-popup v-model="show" mode="right" length="90%">
			<!-- class="form"  -->
			<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">基本信息</view>
					<!-- <u-form-item label="公司:" prop="companyName" label-width="230">
						<js-select v-model="query.companyCode" :showFilter="false" :items="companySelectList"  placeholder="请选择" :tree="true"
							:label-value="query['company.companyName']" @label-input="query['company.companyName'] = $event"></js-select>
					</u-form-item> -->
					<u-form-item  label="商品编码:" prop="viewCode" label-width="230">
						<u-input placeholder="请输入" v-model="query['basInv.viewCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
				
					<u-form-item  label="商品名称:" prop="invName" label-width="230">
						<u-input placeholder="请输入" v-model="query['basInv.invName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="形象刊:" prop="f109" label-width="230">
						<u-input placeholder="请输入" v-model="query.f109" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="货位:" prop="posName" label-width="230">
						<u-input placeholder="请输入" v-model="query['basPosition.posName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="盘点状态:" prop="pdStatus" label-width="230">
						<!-- <u-input placeholder="请输入" v-model="query['cfree1']" type="text" maxlength="200"></u-input> -->
						<js-select v-model="query.pdStatus" dict-type="wms_pd_pdStatus"></js-select>
					</u-form-item>
				</view>
			</u-form>
			<!-- <view class="footer">
			<u-button class="btn" type="primary" @click="submit">查询</u-button>
		</view> -->
			<view class="cu-bar"></view>
			<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
				<!-- round -->
				<button class="cu-btn  lines-red lg " @click="reset">重置</button>
				<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
			</view>
		</u-popup>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        " :style="{ height: computedScrollViewHeight  }">
				<view v-for="(item,index) in list"  class="cu-item shadow " style="position: relative;margin-bottom: 10px;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<view class="title">{{ item.f109 }}</view>

						<view class="">
							<dictLabel style="margin-left: 10px;" :value="item.pdStatus" dict-type="wms_pd_pdStatus" ref="dictLabel">
							</dictLabel>
						</view>
					</view>
					<!-- <view class="cu-form-group">
						<view class="title">商品编码：</view>
						<view style="flex: 1;">  </view>
					</view> -->
					<view class="cu-form-group">
						<view class="title">商品名称：</view>
						<view style="flex: 1;"> {{ item.basInv && 	item.basInv.viewCode ? "【"+item.basInv.viewCode+"】" : ' '  }} {{ item.basInv && 	item.basInv.invName|| ""  }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">货位：</view>
						<view style="flex: 1;"> {{ item.parent && item.parent.basPosition && item.parent.basPosition.posName || ""  }} </view>
					</view>
					<!--  -->
					
					<view class="cu-form-group" style="font-weight: bold;">
						<view>库存数：</view>
						<view style="flex: 1;"> {{ safeNumber(item.iqty) }} </view>
						<view>实盘数：</view>
						<view style="flex: 1;"> {{ safeNumber(item.frealqty) }} </view>
					</view>
					<view class="cu-form-group" style="font-weight: bold;">
						<view class="title">{{ safeNumber(item.yj) >= 0 ? '盘盈' : '盘亏' }} ：</view>
						<view style="flex: 1;"> {{ safeNumber(item.yj) }} </view>
					</view>
					<view class="cu-form-group">
						<view class="title">形象刊：</view>
						<view style="flex: 1;"> {{ item.freeVO&&item.freeVO.cfree1|| ""  }} </view>
					</view>
					<view  class="cu-form-group" v-if="item.pdStatus != '2'">
						<view></view>
						<view class="">
							<button  class="cu-btn lines-green shadow-blur" @click="toForm(item)">盘点</button>
						</view>
					</view>
				
				</view>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>
		<modal ref="modal" @tjconfirm="tjconfirm" ></modal>
		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="toAdd" type="primary"
						style="width: 90px; height: 70px; color: #fff; font-size: 16px">新增
					</u-button>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>
<script>
// 
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	import modal from "./modal.vue";
	export default {
		components: {
			dictLabel,
			modal
		},
		data() {
			return {
				x: 650, //x坐标
				y: 650, //y坐标
				companySelectList:[],
				show: false,
				smshow: false,
				focus: false,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				loadStatus: "loadmore",
				triggered: false,
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				djno:'',
				query:{
					pageNo: 1,
					pageSize: 5,
				},
				model: {
					whcode:'',
				},
			};
		},
		onPullDownRefresh() {
			this.query.pageNo = 1;
			this.loadData();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 2000);
		},
		onReachBottom() {
			if (this.list.length < this.total) {

			};
		},
		onShow() {
			// console.log('页面开启（onLoad）广播监听事件：xwscan')
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
			// 触发自定义事件，通知dictLabel组件重新加载数据
			uni.$emit('dictLabelReload');

		},
		onLoad(e) {
			console.log('onLoad', e);
			this.query.hid = e.id;
			this.model.whcode = e.whcode;
			this.model.companyCode = e.companyCode;
			this.query.pageNo = 1;
			this.loadData();

		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		mounted() {
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			// 安全的数值转换函数，处理undefined、null、空字符串等情况
			safeNumber(value, defaultValue = 0) {
				if (value === null || value === undefined || value === '') {
					return defaultValue;
				}
				const num = Number(value);
				return isNaN(num) ? defaultValue : num;
			},
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
			handleFocus() {
				var _that = this;
				_that.focus = false;
				setTimeout(() => {
					_that.barCode = ''
					_that.focus = true;
				}, 500)
			},
			invCode(bar, companyCode) {
				let InventoryPrefix = this.vuex_config.InventoryPrefix;
				if (bar.indexOf(InventoryPrefix) != -1) {
					return bar
				} else {
					let code = `inv_${companyCode}_${bar}`
					return code
				}
			},
			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					this.confirm()
					//将换行符分割成数组
					// const allItems = newString.split('\n');
					// 	//遍历数组，将每一项添加到arr中
					// 	for(let i = 0;i<allItems.length;i++){
					// 		this.arr.push({
					// 			"content":allItems[i],
					// 			"remarks":this.remarks
					// 		})
					// 	}
				}
			},
			confirm() {
					let _that = this;
					_that.focus = false
					let InventoryPrefix = _that.vuex_config.InventoryPrefix;
					let PositionPrefix = _that.vuex_config.PositionPrefix;
					let InvBarType = _that.vuex_config.InvBarType;
					let PosBarType = _that.vuex_config.PosBarType;
					let bar = encodeURIComponent(this.barCode)
					if (bar.indexOf(PositionPrefix) != -1) {
						this.$u.api.ktnw.getBarInfo({
							barCode: bar,
							barType: PosBarType,
							whCode: this.model.whcode || ''
						}).then((res) => {
							if (res.result == 'true') {
								if(res.data?.basPos?.treeLeaf == '0') {
									this.$forceUpdate()
									_that.sendMp3('sb');
									let message = '请扫描末级货位'
									_that.$refs.jsError.showError("", message, "error");
									return;
								}
								_that.sendMp3('cg');
								this.query['basPosition.posName'] = res.data.basPos.posName
								this.$forceUpdate()
								this.query.pageNo = 1;
								// this.loadData();
								this.loadDataAndCheckAutoOpen();
				
							} else {
								_that.sendMp3('sb');
								let message = res.message
								_that.$refs.jsError.showError("", message, "error");
							}
							this.barCode = ''
						})
					} else {
						if (!this.model.companyCode) {
							_that.sendMp3('sb');
							_that.$refs.jsError.showError("", "请先选择公司", "error");
							setTimeout(() => {
								_that.barCode = ''
							}, 500)
						} else {
							let barCode = this.invCode(bar, this.model.companyCode)
							this.$u.api.ktnw.getBarInfo({
								barCode: barCode,
								barType: InvBarType,
								companyCode: this.model.companyCode
							}).then((res) => {
								if (res.result == 'true') {
									
									if(res.data.basInv){
										_that.sendMp3('cg');
										setTimeout(() => {
											this.barCode = ''
											// _that.focus = true;
										}, 500)
										this.query['basInv.viewCode'] = res.data.basInv?res.data.basInv.viewCode:''
										this.query['basInv.invName'] = res.data.basInv?res.data.basInv.invName:''
										this.$forceUpdate()
										this.query.pageNo = 1;
										// this.loadData();
										this.loadDataAndCheckAutoOpen();

									}else{
										_that.sendMp3('sb');
										let message = '请扫描正确的商品编码'
										_that.$refs.jsError.showError("", message, "error");
									}
								} else {
									_that.sendMp3('sb');
									let message = res.message
									_that.$refs.jsError.showError("", message, "error");
									setTimeout(() => {
										this.barCode = ''
										// _that.focus = true;
									}, 500)
								}
								this.barCode = ''
				
							})
						}
					}
				
				},
			search() {
				let _that = this;
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			
		
			toForm(item){
				const that = this
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
				that.$refs.modal.getJhshow();
				that.$refs.modal.jhData = item;
				// 是否新增
				that.$refs.modal.isAdd = false;
			},
			toAdd(){
				const that = this
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
				that.$refs.modal.getJhshow();
				that.$refs.modal.jhData = {
					djno: that.query.djno,
				};
				that.$refs.modal.whcode = that.model.whcode;
				that.$refs.modal.companyCode = that.model.companyCode;
				that.$refs.modal.isAdd = true;
			},
			toFinsh(item){
				const that = this
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
				that.$u.api.ktnw.overPdBill({
					id: item.id,
				}).then(res=>{
					if (res.result == 'true') {
						that.$u.toast("操作成功");
						that.loadData();
					} else { 
						that.$refs.jsError.showError("", res.message, "error");
					}

				});
			},
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			submit() {
				this.list = [];
				this.query.pageNo = 1;
				this.loadData();
				this.show = false
			},
			startConfirm(e) {
				this.query.planArrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.planArrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// this.headerHeight = 52
					this.headerHeight = 0
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},
			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.ktnw.findPdPosChildInvTask(this.query).then((res) => {
					if (res.length >= 0) {
						// this.xmList = res.list
						if (res.length < 20 || res.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res;
						if (type == "add") {
							for (var i = 0; i < data.length; i++) {
								this.list.push(data[i]);
							}
						} else {
							this.list = data;
						}
						// 遍历list ，计算 盈亏
						for (var i = 0; i < this.list.length; i++) {
							// 使用安全的数值转换函数，确保数值不为undefined
							const frealqty = this.safeNumber(this.list[i].frealqty);
							const iqty = this.safeNumber(this.list[i].iqty);
							this.list[i].yj = frealqty - iqty;
						}


					}
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
			tjconfirm() {
				// 处理modal确认事件
				console.log('Modal确认事件 - 刷新父组件数据');
				// 清空列表并重新加载数据
				this.list = [];
				this.query.pageNo = 1;
				
				// 使用专门的方法来加载数据并检查是否需要自动打开modal
				// this.loadDataAndCheckAutoOpen();
				this.loadData();
				uni.$on('xwscan', this.BroadcastScanningToObtainData)
			},
			// 加载数据并检查是否需要自动打开modal
			loadDataAndCheckAutoOpen() {
				this.$u.api.ktnw.findPdChildTask(this.query).then((res) => {
					if (res.list.length >= 0) {
						// this.xmList = res.list
						if (res.list.length < 20 || res.list.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res.list;
						this.list = data;

						// 遍历list ，计算 盈亏
						for (var i = 0; i < this.list.length; i++) {
							// 使用安全的数值转换函数，确保数值不为undefined
							const frealqty = this.safeNumber(this.list[i].frealqty);
							const iqty = this.safeNumber(this.list[i].iqty);
							this.list[i].yj = frealqty - iqty;
							console.log(this.list[i].yj,'yj',frealqty,iqty)
						}

						// 检查list长度，如果为1则自动弹出modal
						if (this.list.length === 1) {
							// 延迟一下确保DOM更新完成
							setTimeout(() => {
								this.toForm(this.list[0]);
							}, 300);
						}
					}
				});
			},
		},
	};
</script>
<style lang="scss" scoped>
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area {
		z-index: 888;
		height: 97vh;
		width: 650rpx;	
		position: fixed;
		
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 260rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>