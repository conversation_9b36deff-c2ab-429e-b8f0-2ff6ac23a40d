<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<!-- <u-sticky class="u-sticky">
			<view class="cu-bar search" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
					@search="smconfirm"></u-search>
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="search" name="scan" size="50"></u-icon>
				</view>
			</view>
		</u-sticky> -->
		<u-form class="form bg-white" :model="model" ref="uForm" label-position="left">
			<u-form-item label="单据号:" prop="djno" label-width="200"  >
				<u-input placeholder=" " v-model="model.djno" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="商品编码:" prop="viewCode" label-width="200"  >
				<u-input placeholder=" " v-model="model.basInv.viewCode" type="text" :disabled="true" di maxlength="64"></u-input>
				<!-- position: fixed;top: 80rpx;left: 20rpx; -->
				<view v-if="invFlag" @click="handleFocus"
					style="text-align: center;margin: 0 20rpx;">
					<u-icon name="checkmark-circle" color="#81b337" size="60"></u-icon>
					<view style="color: #81b337"> 验证通过 </view>
				</view>
				<view v-if="!invFlag" @click="handleFocus"
					style="text-align: center;left: 20rpx;margin: 0 20rpx;">
					<u-icon name="info-circle" color="#ff9900" size="60"></u-icon>
					<view style="color: #ff9900"> 暂未验证 </view>
				</view>
			</u-form-item>
			<u-form-item label="商品名:" prop="invName" label-width="200"  >
				<u-input placeholder=" " v-model="model.basInv.invName" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<!-- <u-form-item label="公司:" prop="cusname" label-width="200"  >
				<u-input placeholder=" " v-model="model.basVen.cusname" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item> -->
			
			<u-form-item label="形象刊:" prop="cfree1" label-width="200" >
				<u-input placeholder=" " v-model="model.freeVO.cfree1" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<view style="display: flex;">
				<u-form-item label="应盘:" prop="fqty" label-width="100"  >
					<u-input placeholder=" " v-model="model.fqty" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item label="已盘:" prop="sumJhQty" label-width="100"  style="margin: 0 10px;">
					<!-- {{model.sumJhQty || 0}} / {{model.syQty || 0}} -->
					<u-input placeholder=" " v-model="model.sumJhQty" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item label="剩余:" prop="syQty" label-width="100"  >
					<u-input placeholder=" " v-model="model.syQty" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
		</u-form>
		
		<view class="action bg-white "
			style="color: #3E97B0;font-size: 20px;align-items: center;display: flex;padding: 0 0 10px 10px;">
			<!-- <u-icon name="/static/image/detail.png" size="80"></u-icon> -->
			<text class="cuIcon-titles text-bold">盘点单明细</text>
		</view>
		<view style="padding: 10px;">
			<view v-for="(item,index) in children" :id="'id'+ item.id" class="cu-item shadow " style="position: relative;" :key="index">				
				<view class="cu-form-group">
					<view class="title">货位：</view>
					<view style="flex: 1;"> {{ item.cposName || ""  }} {{ item.cposCode ? '('+ item.cposCode +')' :''  }}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">已盘货数：</view>
					<view style="flex: 1;"> {{ item.sumQty|| ""  }} </view>
					<!--  v-if="item.jhStatus == 2"-->
					<button class="cu-btn  bg-confirm lg  " @click="thClick(item)" v-if="item.sumQty && model.jhStatus!=0">退回</button>
				</view>
				<view class="cu-form-group">
					<view class="title">剩余盘货：</view>
					<view style="flex: 1;"> {{ item.syJhQty || '0'  }} </view>
					
				</view>
				<!-- <view class="cu-form-group">
					<view class="title">送货数：</view>
					<view style="flex: 1;"> {{ item.iqty|| ""  }} （{{ item.basInv.measname|| ""  }} ）</view>
				</view> -->
				<!-- <view class="cu-form-group text-red text-bold">
					<view class="title">本次拣货：</view>
					<view style="flex: 1;"> 
						 <view style="width: 100%;" @click="changeIqty(item,index)">{{ item.jhQty }}</view>
					</view>
				</view> -->
			</view>

	</view>
	
	<u-modal v-model="thshow" title="退回" @confirm="thconfirm" :show-cancel-button="true" width="80%" @cancel="thCancel">
		<!-- <view class="slot-content">
			<view class="cu-bar" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请扫描ASN单号"
					:show-action="false" @search="confirm"></u-search>
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="search" name="scan" size="50"></u-icon>
				</view>
			</view>
		</view> -->
		<u-form class="form bg-white" :model="model" ref="uForm" label-position="left" style="padding: 0 10px;">
		<view style="display: flex;justify-content: space-between;">
			<view style="flex: 1;">
				<u-form-item label="退回货位:" prop="posName" label-width="180" :label-style="{ 'font-weight': 'bold' ,'color':'red'}">
					<!-- <u-search search-icon='' v-model="model.posName" ref="uSearch"  placeholder="请先聚焦后扫描" :clearabled="false" :show-action="false" @search="smconfirm('PosName')" bgColor="#fff" :focus="thFocus"></u-search> -->
					<view style="width: 100%;">
						{{model.posName}}
					</view>
					<u-icon  @click="xzhw" name="arrow-right"  size="70"></u-icon>
				</u-form-item>
			</view>
		</view>
		<u-form-item label="退回数量:" prop="thIqty" label-width="180"
			:label-style="{ 'font-weight': 'bold','color':'red' }">
			<u-input v-model="model.thIqty" type="number" placeholder="请输入" clearable />
		</u-form-item>
		</u-form>
	</u-modal>

	<u-modal v-model="jhshow" title="本次盘点" @confirm="tjconfirm" :show-cancel-button="true" width="80%" @cancel="cancel">
		<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
			<u-form-item label="商品:" prop="cposCode" label-width="100">
				{{ model.basInv.invName }}
			</u-form-item>
		</u-form>
		<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
			<u-form-item label="货位:" prop="cposCode" label-width="100">
				{{ jhData.cposName }}
			</u-form-item>
		</u-form>
		<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;">
			<u-form-item label="剩余数量:" prop="cposCode" label-width="180">
				{{ jhData.syJhQty || 0 }}
			</u-form-item>
		</u-form>
		<u-form class="form bg-white" :model="jhData" ref="uForm" label-position="left" style="padding: 0 10px;" >
			<u-form-item label="盘点数量:" prop="iqty" label-width="180"
				:label-style="{ 'font-weight': 'bold','color':'red' }">
				<!-- type="digit" :focus="jhQtyFocus" -->
				<u-input v-model="jhData.jhQty"   type="number" placeholder="请输入" @input="replaceInput" clearable />
			</u-form-item>
		</u-form>
	</u-modal>
	
	<!-- <view>
		<movable-area class="movable-area" v-if="model.jhStatus!=2">
			<movable-view class="movable-view" :x="x" :y="y" direction="all">
				<u-button size="mini" @click="submit" type="primary"
					style="width: 90px; height: 70px; color: #fff; font-size: 16px">确认拣货
				</u-button>
			</movable-view>
		</movable-area>
	</view> -->
	<!-- <view>
		<movable-area class="movable-area">
			<movable-view class="movable-view" :x="x" :y="y" direction="all">
				<u-button size="mini" @click="handleFocus" type="success"
					style="width: 90px; height: 70px; color: #fff; ">
					<view style="font-size: 16px;display: flex; flex-direction: column;text-align: center;">
						<u-icon style="margin: auto;" name="/static/jeesite/sm1.png" size="80"></u-icon>
						<view >扫一扫</view>
					</view>
				</u-button>
				
			</movable-view>
		</movable-area>
	</view> -->
	</view>
</template>

<script>
	import config from '@/common/config.js';
	export default {
		data() {
			return {
				jhData:{},
				thshow:false,
				jhshow:false,
				barCode: "",
				jhQtyFocus: false,
				focus: false,
				shQty:'',
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				invFlag:false,
				model: {
					basInv: {

					},
					freeVO: {

					},
				},
				//主表id
				query: {
					id: '',
				},
				//子表id
				querys: {
					id: '',
				},
				children: [],
				carVenSelectList: [],
				pickerTime: false, //控制日期显示
				currentCposCode:{},
				currentThCposCode:{},
				x: 650, //x坐标
				y: 650, //y坐标
				thFocus:false,
				paramsId : ''
			}
		},
		onLoad(params) {
			if(params.id){
				this.paramsId = params.id
				this.loadList({id:params.id});
			}
			uni.$on('hwObjs', (arr) => {
				if(arr.length){
					this.model.posCode = arr[0].id
					this.model.posName = arr[0].name
					this.$forceUpdate()
				}
			})
		},
		onShow() {
			// console.log('页面开启（onLoad）广播监听事件：xwscan')
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			handleFocus(){
				console.log('focus',this.focus)
				this.focus = false;
				setTimeout(()=>{
					this.focus = true;
				},500)
				console.log('focus',this.focus)
			},
			thClick(item){
				this.thshow = true
				this.currentThCposCode = item,
				this.model.posName = item.cposName
				this.model.posCode = item.cposCode
				this.thFocus = false
				setTimeout(()=>{
					this.thFocus = true
				})
			},
			thCancel(){
				console.log('thCancle')
				this.thshow = false
				this.currentCposCode = {}
				this.model.posName = ''
				this.model.posCode = ''
				this.model.thIqty = ''
				this.thFocus = false
				this.handleFocus()
			},
			changeIqty(item,index){
				console.log(item,'item====');
				// this.model.iqty = item.arrQty
				this.jhshow = true
				this.currentCposCode = {
					cposCode:item.cposCode,
					cposName:item.cposName,
				}
			},
			cancel(){
				this.jhshow = false
				this.currentCposCode = {}
				this.model.iqty = ''
				this.handleFocus()
			},
			thconfirm(){
				var _that = this;
				const findItem = _that.children.find(item => item.id == _that.currentThCposCode.id)
				if(_that.model.thIqty > _that.model.sumJhQty){
					let message = '退回数量不能大于已盘点数量'
					this.thshow = true
					_that.$refs.jsError.showError("", message, "error");
					return ;
				}
				const findItemList = [{
					...findItem,
					jhQty: _that.model.thIqty,
					cposCode : _that.model.posCode,
					cposName : _that.model.posName
				}]
				_that.$u.api.ktnw.jhCallBack({
					id:_that.model.id,
					jhPickDetailsPosList: findItemList,
				}).then(res => {
					
					if(res.result == 'true'){
						_that.currentThCposCode = {}
						_that.model.posName = ''
						_that.model.posName = ''
						_that.model.thIqty = ''
						this.loadList({id:this.paramsId});
						this.handleFocus()
					}else{
						_that.$refs.jsError.showError("", res.message, "error");
					}
					// console.log()
				})
			},
			tjconfirm(){
				var _that = this;
				if(_that.jhData.jhQty > _that.jhData.syQty){
					let message = '盘点数量不能大于剩余盘点数量'
					_that.$refs.jsError.showError("", message, "error");
					return;
				}
				if(!_that.jhData.jhQty || _that.jhData.jhQty == 0){
					let message = '盘点数量不能为零'
					_that.$refs.jsError.showError("", message, "error");
					return;
				}
				this.$u.api.ktnw.jhConfirm({
					id: this.model.id,
					jhPickDetailsPosList: [
						_that.jhData
					],
				}).then(res => {
					if (res.result == 'true') {
						_that.sendMp3('cg');
						this.$u.toast(res.message);
						_that.currentThCposCode = {}
						_that.model.posName = ''
						_that.model.posName = ''
						_that.model.thIqty = ''
						_that.jhData ={}
						// this.loadList({id:this.paramsId});
						this.jhshow = false
						// this.handleFocus()
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						_that.sendMp3('sb');
						this.$refs.jsError.showError('',res.message,'error');
					}
				});
				
			},
			tjconfirm2(){
				var _that = this;

				const findPosCode = _that.children.findIndex(item => item.cposCode == _that.currentCposCode.cposCode)
				if(findPosCode != -1){
					if(_that.model.iqty > _that.model.syQty){
						let message = '盘点数量不能大于剩余盘点数量'
						this.jhshow = true
						_that.$refs.jsError.showError("", message, "error");
					}else{
						_that.children[findPosCode].jhQty = _that.model.iqty
						_that.children[findPosCode].yzPos = true
						_that.$forceUpdate()
						// 计算意见数量和剩余数量
						_that.model.sumJhQty = _that.children.reduce((total, item) => {
							// console.log(item.arrQty,'item.arrQty')	
							return total + Number(item.jhQty) || 0;
						}, this.model.sumJhQty || 0)
						
						_that.model.syQty = Number(_that.model.fqty) - Number(_that.model.sumJhQty);
						// console.log(_that.model.syQty,'model.syQty',yjQty,_that.model.yjQty)
						
						_that.$forceUpdate()
						
						
						// _that.model.syQty = Number(_that.model.fqty) || 0 - Number(_that.model.cqty) || 0;
						
						uni.pageScrollTo({
							selector:'#id'+_that.children[findPosCode].id,
							duration:300
						})		
						this.handleFocus()
						setTimeout(() => {
							_that.currentCposCode = {}
							this.model.iqty = ''
						})	
					}
					
				}
			},
			replaceInput(e) {
				console.log(e);
				var that = this
				e = e.match(/^\d*(\.?\d{0,2})/g)[0]
				this.$nextTick(() => {
					that.model.iqty = e
				})
			
			},
			invCode(bar, companyCode) {
				let InventoryPrefix = this.vuex_config.InventoryPrefix;
				if (bar.indexOf(InventoryPrefix) != -1) {
					return bar
				} else {
					let code = `inv_${companyCode}_${bar}`
					return code
				}
			},
			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				if(this.jhshow){
					return false;
				}
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					
					if(this.thshow){
						this.smconfirm('PosName')
					}else{
						this.smconfirm()
					}
				}
			},
			smconfirm(PosName){
				let _that = this;
				_that.focus = false
				let InventoryPrefix = _that.vuex_config.InventoryPrefix;
				let PositionPrefix = _that.vuex_config.PositionPrefix;
				let InvBarType = _that.vuex_config.InvBarType;
				let PosBarType = _that.vuex_config.PosBarType;
				let bar = ''
				if(PosName == 'PosName'){
					// bar =  encodeURIComponent(this.model.posName)
					bar =  encodeURIComponent(this.barCode)
					this.model.posName = ''
				}else{
					bar = encodeURIComponent(this.barCode)
				}
				console.log('bar',bar)
				// if (bar.indexOf(InventoryPrefix) != -1) {
				// 	this.$u.api.ktnw.getBarInfo({
				// 		barCode: bar,
				// 		barType: InvBarType
				// 	}).then((res) => {
				// 		if(res.result == 'true'){
				// 			_that.sendMp3('cg');
				// 			setTimeout(() => {
				// 				_that.barCode = ''
				// 				_that.focus = true;
				// 			}, 500)
				// 			_that.invFlag = true
				// 			_that.$forceUpdate()
							
							
				// 		}else{
				// 			setTimeout(() => {
				// 				_that.barCode = ''
				// 				_that.focus = true;
				// 			}, 500)
				// 			_that.sendMp3('sb');
				// 			let message = res.message
				// 			_that.$refs.jsError.showError("", message, "error");
							
				// 		}
						
				// 	})
				// }else 
				if (bar.indexOf(PositionPrefix) != -1) {
					this.$u.api.ktnw.getBarInfo({
						barCode: bar,
						barType: PosBarType,
						whCode:_that.model.parent.whcode || ''
					}).then((res) => {
						if(res.result == 'true'){
							if(res.data?.basPos?.treeLeaf == '0') {
								this.$forceUpdate()
								_that.sendMp3('sb');
								let message = '请扫描末级货位'
								_that.$refs.jsError.showError("", message, "error");
								return;
							}
							if(PosName == 'PosName'){
								_that.sendMp3('cg');
								_that.model.posCode = res.data.bizKey
								_that.model.posName = res.data.name
								_that.$forceUpdate()
								setTimeout(() => {
									_that.barCode = ''
									_that.focus = true;
								}, 500)
							}else{
								const posCode = res.data.bizKey
								const findPosCode = _that.children.findIndex(item => item.cposCode == posCode)
								
								console.log(findPosCode,'findPosCode',posCode)
								if(findPosCode != -1){
									_that.sendMp3('cg');
									setTimeout(() => {
										_that.barCode = ''
										// _that.focus = true;
									}, 500)
									
									 _that.children.forEach(item=>{
										 if(item.cposCode == posCode){
											 _that.jhData = {
												 ...item,
												 cposCode:posCode,
												 cposName:res.data.name,
												 jhQty:item.syJhQty || 0,
											 }
										 }
									 })
									_that.currentCposCode = {
										cposCode:posCode,
										cposName:res.data.name,
									}
									_that.jhshow = true
									// _that.jhQtyFocus = false
									// setTimeout(() => {
									// 	_that.jhQtyFocus = true;
									// }, 500)
									
									
								
								
									
									_that.$forceUpdate()
								}else{
									setTimeout(() => {
										_that.barCode = ''
										_that.focus = true;
									}, 500)
									_that.sendMp3('sb');
									let message = '请扫码对应的货位码'
									_that.$refs.jsError.showError("", message, "error");
									
								
								}
							}
							
							
							
						}else{
							setTimeout(() => {
								_that.barCode = ''
								_that.focus = true;
							}, 500)
							_that.sendMp3('sb');
							let message = res.message
							_that.$refs.jsError.showError("", message, "error");
							
						}
					})
				}else {
					
					let barCode = this.invCode(bar, this.model.parent.companyCode)
						this.$u.api.ktnw.getBarInfo({
							barCode: barCode,
							barType: InvBarType,
							companyCode: this.model.parent.companyCode
						}).then((res) => {
							if(res.result == 'true'){
								if(res.data.basInv && this.model.basInv.invCode == res.data.basInv.invCode){
									_that.sendMp3('cg');
									setTimeout(() => {
										_that.barCode = ''
										_that.focus = true;
									}, 500)
									_that.invFlag = true
									_that.$forceUpdate()
								}else{
									setTimeout(() => {
										_that.barCode = ''
										_that.focus = true;
									}, 500)
									_that.sendMp3('sb');
									let message = '请扫描对应的商品码'
									_that.$refs.jsError.showError("", message, "error");
								}
								
							}else{
								setTimeout(() => {
									_that.barCode = ''
									_that.focus = true;
								}, 500)
								_that.sendMp3('sb');
								let message = res.message
								_that.$refs.jsError.showError("", message, "error");
								
							}
							
						})
					
					
					// setTimeout(() => {
					// 	_that.barCode = ''
					// 	_that.focus = true;
					// }, 500)
					// _that.sendMp3('bcz');
					// _that.$refs.jsError.showError("", "请扫描正确的商品码或货位码", "error");
					
				}
				
				
				
			},
			xzhw(){
				let whcode =  this.model.whcode || ''
				uni.navigateTo({
					url: "/pages/ktnw/qtsj/hwXz?whcode=" + whcode,
				});
			},
			paste(){
				let that = this
				uni.getClipboardData({
					success: (res) => {  // 获取成功回调
						that.model.carNo = res.data.trim().slice(0,8)
						that.$forceUpdate()
						if(!that.model.cdriver && !that.model.driverPhone){
							that.$u.api.mffh.getCarInfoByCarNo({carNo:that.model.carNo}).then(res => {
								that.model.cdriver = res.data.cdriver || '';
								that.model.driverPhone = res.data.driverPhone || '';
								that.$forceUpdate()
							});
						}
					}
				})
			},
			// cancel() {
			// 	// uni.$emit('refreshData');
			// 	uni.navigateBack({
			// 		delta: 1,
			// 	})
			// },			
			loadList(data) {
				this.$u.api.ktnw.listDataInvAndPos(data).then(res => {
					// console.log(res,'res')
					if(res.result == 'true'){
						
						this.model = {
							...this.model,
							...res.data,
						}
						const syQty = (Number(this.model.fqty) || 0) - (Number(this.model.sumJhQty) || 0)
						this.model.syQty = syQty
						console.log('syQty',Number(this.model.fqty) ,Number(this.model.sumJhQty) )
						this.children = res.data.jhPickDetailsPosList || []
						this.children = this.children.map(item => {
							return {
								...item,
								jhQty:'',
								yzPos:false,
							}
						})
						// this.$forceUpdate()
					}
				})
			},

			submit() {
				const everyArrQty = this.children.some(item => item.jhQty != '')
				console.log(everyArrQty,'everyArrQty',this.children)
				if(!everyArrQty){
					this.$refs.jsError.showError('','拣货数量必填！','error');
					return;
				}

				// 只保留this.children 每一条数据的yzPos 为true的数据
				// this.children = this.children.filter(item => item.yzPos)
				const jhPickDetailsPosList = this.children.filter(item => item.yzPos)
				console.log(this.children,'this.children')
				
				// this.model.asnCList = this.children
				this.$u.api.ktnw.jhConfirm({
					id: this.model.id,
					jhPickDetailsPosList: jhPickDetailsPosList,
				}).then(res => {
					if (res.result == 'true') {
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				});
			},
			sendMp3(name) {
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
		}
	}
</script>
<style lang="scss" scoped>
	/* .footer {
	position: fixed;
	bottom: 0;
	right:0;
	width: 100%;
	line-height: var(--footer-height);
	background: #ffffff;
	color: #ffffff;
} */
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	.btn-plus {
		position: fixed;
		bottom: 260rpx;
		right: 40rpx;
		z-index: 2;
		opacity: 0.6;
	}

	.btn-plus:hover {
		opacity: 1;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */

	}

	.cu-bar {
		min-height: 80px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 200rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}
	

	.cu-modal-footer {
		padding: 32rpx 32rpx !important;
		width: 100% ;
		.cu-btn {
			width: 50%;
		}
		
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
	
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;z-index: 999;
		border-top: 1px solid #eee;
	}
	/deep/ .u-model__title[data-v-3626fcec]{
		color: #fff !important;
		padding: 10px !important;
		background: #3e97b0 !important;
		font-weight: bold !important;
	}
</style>