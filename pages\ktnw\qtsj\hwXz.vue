<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<u-sticky class="u-sticky">
			<!-- bg-blue @tap="show=true" -->
			<view class="padding-sm flex light " style="background-color: #eee;" >
				<view style="width: 100%"><u-search placeholder="搜索" v-model="keyword" bg-color="#fff"
						:show-action="true"   @custom="search" @search="search"></u-search>
				</view>
				<!-- <u-icon @click="show=true" :name="filterIcon" size="60"
					class="margin-sm-left flex-sub margin-sm-right"></u-icon> -->

			</view>
		</u-sticky>
		
		<!-- :style="[{height:'calc(100vh - '+ CustomBar + 'px - 50px)'}]" -->
		<scroll-view v-if="rangeList.length" scroll-y="true" class="indexes" :scroll-with-animation="true" :enable-back-to-top="true"
			 style="padding-top: 10px;">
			<next-tree v-if="rangeList.length" ref="qiantree" :selectParent="false" labelKey="name" valueKey="id" 
				:multiple="false" :treeData="rangeList"  
				@confirm="onconfirm" />
		</scroll-view>
		<view style="padding-top:200rpx ;">
			<u-empty v-if="!rangeList.length"></u-empty>
		</view>
		<view style="height: 70px;"></view>
		<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
			<button class="cu-btn  lines-red lg" @click="cancel">关闭</button>
			<button class="cu-btn  bg-confirm lg  margin-left" @click="submit">确定</button>
		</view>
	</view>
</template>
<script>
	import nextTree from '@/components/treeList/tree.vue'
	import {
		hasPermission
	} from '@/common/fire.js'
	export default {
		components: {
			nextTree
		},
		data() {
			return {
				rangeList:[
				],
				whcode: '',
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
				},
				loadStatus: "loadmore",
				triggered: false,
				// flag: hasPermission('app:proj:weekly:pmWeekly:edit'),
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				type:'',
			};
		},
		onShow() {
			
		},
		onLoad(e) {
			this.whcode = e.whcode
			if(e.type){
				this.type = e.type
			}
			this.search()
		},
		mounted() {
			// this.$refs.xmInfo.$on('child-mounted-done', () => {
			//   this.calculateScrollViewHeight();
			// });
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			search(){
				let treeLeaf = ''
				if(this.keyword){
					treeLeaf = 1
				}
				// ,whcode:this.whcode
				this.$u.api.ktnw.basPositionTreeData({treeLeaf:treeLeaf,isShowCode:false,posName:this.keyword,whcode:this.whcode}).then(res => {
					if(this.keyword){
						this.rangeList = res
					}else{
						this.rangeList = this.buildTree(res,'0')
					}
				});
			},
			buildTree(data, pid) {
			    return data.filter(item => item.pId === pid).map(item => ({
			        id: item.id,
			        name: item.name,
			        children: this.buildTree(data, item.id)
			    }));
			},
			cancel(){
				uni.navigateBack({
					delta: 1,
				});
			},
			submit() {
				this.$refs.qiantree._confirm()
			},
			onconfirm(data){
				
				data.arr.forEach((item)=>{
					item.type =  this.type
				})
				uni.$emit('hwObjs',data.arr);
				uni.navigateBack({
					delta: 1,
				})
				
			},
			carNoconfirm(v){
				this.query['mfCarplanFhH.carNo'] = v;
				this.$forceUpdate()
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.query['mfCarplanFhH.carNo'])
			},
			editDetail(model) {
				const that = this
				uni.navigateTo({
					url: '/pages/mf/fh/fhEdit?id=' + model.id,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						Filter(data) {

						}
					},
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// res.eventChannel.emit('initfilter', that.filter)
					}
				})
			},
			erpPush(model) {
				const that = this;
				uni.showModal({
					title: '修改提示',
					content: '确认推送ERP吗？',
					confirmColor: '#00007f',
					success: (res) => {
						if (!res.confirm) {
							return false;
						} else {
							this.$u.api.mffh.mfCarplanFhHpushdata({
								ids: model.id,
								planType: 1
							}).then(res => {
								if (res.result == 'true') {
									that.$u.toast(res.message);
									that.loadData();
								} else {
									this.$refs.jsError.showError('',res.message,'error');
								}
							})
						}
					}
				})
			},
			
			
			
			delDetail(id) {
				// this.query.id = id;
				uni.showModal({
					title: '删除提示',
					content: '是否删除此详情？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							this.isdisabled = true
							return false;
						}
						this.$u.api.mffh.delDetail({
							id
						}).then(res => {
							if (res.result=="true") {
								this.$u.toast("删除成功！");
							} else {
								this.$refs.jsError.showError('',res.message,'error');
							}
							// this.query.id = ''
							// this.list = [];
							this.query.pageNo = 1;
							this.loadData();
						})
					}
				})
			},
			
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// await new Promise((resolve) => {
					//   this.$nextTick(() => {
					//     this.headerHeight = this.$refs.xmInfo.$refs['u-sticky'].height + this.$refs.xmInfo.$refs['u-sticky'].h5NavHeight + this.$refs.navbar.navbarHeight;
					//     resolve();
					//   });
					// });
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

		}
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>