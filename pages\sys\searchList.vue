<template>
	<view class="wrap ">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="0" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->
		<view v-for="res in menuList1" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<!-- margin-bottom -->
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 15rpx;">
					<!-- v-if="item.isSHow" -->
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
				<!-- <u-skeleton :loading="loading" :animation="true" bgColor="#FFF"></u-skeleton> -->
			</view>
		</view>
		

	</view>
</template>

<script>
	import checkVersion from "@/pages/lq-upgrade/checkVersion.js";
	export default {
		data() {
			return {
				stringPermissions: [],
				imgList: [
				],
				todoCount: 0,
				menuList1: [],
				menuList: [
					// 现存量查询
					{
						extend:{
							extendS2:'menue:data:xcl'
						},
						menuIcon: '/static/image/ktnw/xcl.png',
						url: '/pagesData/data/stockListData',
					},
					// 货位存量查询
					{
						extend:{
							extendS2:'menue:data:hwcl'
						},
						menuIcon: '/static/image/ktnw/hwcl.png',
						url: '/pagesData/data/posSumListData',
					},
					{
						extend:{
							extendS2:'menue:data:hwsccx'
						},
						menuIcon: '/static/image/ktnw/hwcl.png',
						url: '/pagesData/data/posListData',
					},
					
				],
			}
		},
		onShow() {
		},
		created() {
			 
		},
		onLoad() {
			this.upgrade();
			var _this = this;
			this.$u.api.menuTree().then(res => {
				if (res.length > 0) {
					res.forEach(item => {
						if ('系统管理' == item.menuName) {
							item.childList.forEach(item2 => {
								if ('移动端管理' == item2.menuName) {
									// 查找item2.childList的数组对象中是否存在名称为"数据查询"的数据，只把这条数据传递给this.showMenu方法
									// this.showMenu(item2.childList);
									item2.childList.forEach(item3 => {
										if ('数据菜单' == item3.menuName) {
											this.showMenu(item3.childList);
										}
									})
								}
							})

						}
					})

				}
			});
			// this.$u.api.authInfo().then(res => {
			// 	this.stringPermissions=(res==null || res.stringPermissions==null)?[]:res.stringPermissions;
			// });
		},
		methods: {
			//检查版本更新
			upgrade() {
			  // #ifdef APP-PLUS
			  this.$u.api.upgradeCheck().then((res) => {
			    let url = res.data.apkUrl
			      ? res.data.apkUrl
			      : this.vuex_config.xtUrl + res.data.xtUrl;
			    if (res.result == "true") {
			      checkVersion({
			        name: res.data.upTitle, //最新版本名称
			        code: res.data.upVersion, //最新版本号
			        content: `${res.data.upContent}`, //更新内容
			        url, //下载链接
			        // forceUpdate: true, //是否强制升级
			        forceUpdate: res.data.upType == '3'?true:false, //是否强制升级
			      });
			    }
			  });
			  // #endif
			},
			//显示菜单
			showMenu(list) {
				this.menuList1 = list;

				// 假设 extendS2 是唯一的，并且我们想要根据它来匹配项目
				// 创建一个映射来快速查找 menuList 中的项目
				const menuItemMap = new Map();
				this.menuList.forEach(item => {
					
					const extendS2 = item.extend?.extendS2;
					if (extendS2 !== undefined) {
						menuItemMap.set(extendS2, { menuIcon: item.menuIcon, url: item.url });
					}
					console.log(menuItemMap,'extendS2')
				});

				// 遍历 menuList1，并使用映射来快速查找和更新相关属性
				this.menuList1.forEach(res => {
					res.childList.forEach(req => {
						const extendS2 = req.extend?.extendS2;
						console.log(extendS2,req.menuName,'req.menuName===');
						if (menuItemMap.has(extendS2)) {
							const { menuIcon, url } = menuItemMap.get(extendS2);
							req.menuIcon = menuIcon;
							req.url = url;
							req.isSHowOnce = true;
						}{
							// 如果没有找到匹配的extendS2，则不显示该菜单项
							req.isSHowOnce = false;
						}					
					});
				});
				console.log(this.menuList1,'this.menuList1===');
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			imgListClick(index) {
				console.log(`点击了第${index + 1}页图片`)
			},
			itemClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style scoped lang="scss">
	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}
</style>