
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
{
    "name" : "数字化WMS平台",
    "appid" : "__UNI__1C647E4",
    "description" : "重庆轻企信息技术有限公司",
    "versionName" : "33.0.0",
    "versionCode" : 133,
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        // APP-VUE分包，可提APP升启动速度，2.7.12开始支持，兼容微信小程序分包方案，默认关闭
        "optimization" : {
            "subPackages" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {
            "Barcode" : {},
            "Bluetooth" : {},
            "Camera" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_CHECKIN_PROPERTIES\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.EXPAND_STATUS_BAR\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MEDIA_CONTENT_CONTROL\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_FORMAT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CALL_LOG\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "minSdkVersion" : 22
            },
            "ios" : {
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "oauth" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common"
            }
        },
        "nativePlugins" : {
            "Mpaas-Scan" : {
                "AppId" : "ALIPUB1653480281448",
                "License" : "wXfAjruEMp2Od1mX3ubAW5KCJow1SqdX+H/c/DTqcvsJQ6Iq1L7M6q6C9wLxSayatK5usONSvDQuHGui4kcYZl1g/FsGK4ynTxVAeYFQAw24UfcUINhUQK4zFlas8qfCWPuXI1YbyOmOFDb/T9xwoQ7X5I+0XnxgAGYZbPkMdR9Utpj5uK0S1b/NqmuGJKniBrq2taqvyXIKHpliVrOYcATpkBifZzi+fGkBlBnmSitm4s7BslJ6VFAcMS/YWJbc5HYuEiAyEc6rlY7RFwq+W1yUqKevN2w+CjK1oizN0NHY0GFZsTQYop7KzUlfoc4/s4A6lGRR8hc4tWn8jLWeiw==",
                "WorkspaceId" : "default",
                "__plugin_info__" : {
                    "name" : "支付宝原生扫码插件",
                    "description" : "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=2636",
                    "android_package_name" : "uni.UNI1C647E4",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "2636",
                    "parameters" : {
                        "AppId" : {
                            "des" : "Android平台的AppId，请填写Android的config文件中的appId对应的值",
                            "key" : "mobilegw.appid",
                            "value" : ""
                        },
                        "License" : {
                            "des" : "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值",
                            "key" : "mpaasConfigLicense",
                            "value" : ""
                        },
                        "WorkspaceId" : {
                            "des" : "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值",
                            "key" : "workspaceId",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wxb6cd931ce721e462",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.writePhotosAlbum" : {
                "desc" : "需要保存图片到相册权限"
            },
            "scope.readWritePhotosAlbum" : {
                "desc" : "需要读写相册权限"
            }
        },
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "component2" : true
    },
    "mp-qq" : {
        "optimization" : {
            "subPackages" : true
        },
        "appid" : ""
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "h5" : {
        "template" : "h5.html",
        "router" : {
            "mode" : "hash",
            "base" : "/app"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "title" : "JeeSite",
        "domain" : "/app"
    }
}
