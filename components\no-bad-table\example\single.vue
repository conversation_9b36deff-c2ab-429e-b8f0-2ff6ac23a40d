<template>
	<view class="example">
		<view class="title">简单table</view>
		<v-table :columns="columns" :list="data"></v-table>
	</view>
</template>

<script>
	import vTable from "@/components/no-bad-table/table.vue"
	export default{
		components: {
			vTable
		},
		data(){
			return {
				data: [{
						name: '<PERSON>',
						age: 18,
						address: 'New York No. 1 Lake Park',
						id: "1",
						
					},
					{
						name: '<PERSON>',
						age: 25,
						address: 'London No. 1 Lake Park',
						id: "2"
					},
					{
						name: '<PERSON>',
						age: 30,
						address: 'Sydney No. 1 Lake Park',
						id: "3"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "4"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "5"
					},
				
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "6"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "7"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "8"
					},
					{
						name: '<PERSON>',
						age: 26,
						address: 'Ottawa No. 2 Lake Park',
						id: "9"
					}
				],
				columns: [{
						title: "ID",
						key: "id"
					},
					{
						title: 'Name',
						key: 'name'
					},
					{
						title: 'Age',
						key: 'age'
					},
					{
						title: 'Address',
						key: 'address'
					}
				],
			}
		}
		
	}
</script>

<style>
</style>
