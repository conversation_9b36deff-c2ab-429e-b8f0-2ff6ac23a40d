<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<u-sticky class="u-sticky">
			<view class="padding-sm flex light " style="background-color: #eee;" @tap="show=true">
				<view style="width: 100%"><u-search placeholder="单号/公司/仓库等" v-model="keyword" bg-color="#fff"
						:show-action="false" :disabled="true" @tap="show=true" searchIconSize="26"
						:inputStyle="inputStyle"></u-search>
				</view>
			</view>
		</u-sticky>
		<!-- <view class="cu-bar search" style="padding: 10px">
			<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
				@search="confirm"></u-search>
			<view style="margin-left: 10px; display: flex; flex-direction: column">
				<u-icon @click="show=true" name="list-dot" size="50"></u-icon>
			</view>
		</view> -->
		<u-popup v-model="show" mode="right" length="90%">
			<!-- class="form"  -->
			<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">基本信息</view>
					<u-form-item label="公司:" prop="companyName" label-width="230">
						<js-select v-model="query.companyCode" :showFilter="false" :items="companySelectList"  placeholder="请选择" :tree="true"
							:label-value="query['company.companyName']" @label-input="query['company.companyName'] = $event"></js-select>
					</u-form-item>
				
					<u-form-item  label="拣货单号:" prop="djno" label-width="230">
						<u-input placeholder="请输入" v-model="query['djno']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<!-- <u-form-item  label="仓库:" prop="cwhname" label-width="230">
						<u-input placeholder="请输入" v-model="query['basWare.cwhname']" type="text" maxlength="200"></u-input>
					</u-form-item> -->
					
					<u-form-item  label="拣货状态:" prop="jhStatusIn" label-width="230">
						<js-select v-model="query['jhStatusIn']" :showFilter="false" dict-type="jh_main_status"  placeholder="请选择"></js-select>
					</u-form-item>
				</view>				
			</u-form>
			<view class="cu-bar"></view>
			<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
				<!-- round -->
				<button class="cu-btn  lines-red lg " @click="reset">重置</button>
				<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
			</view>
		</u-popup>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<!-- :style="{ height: computedScrollViewHeight  }" -->
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        " >
				<view v-for="(item,index) in list"  class="cu-item shadow " style="position: relative;margin-bottom: 10px;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<view class="text-red text-bold"> {{ item.djno|| ""  }}</view>
						<view class="">
							<dictLabel style="margin-left: 10px;" :value="item.jhStatus" dict-type="jh_main_status">
							</dictLabel>
						</view>
					</view>
					
					<view class="cu-form-group">
						<view class="title">公司：</view>
						<view style="flex: 1;">
							{{ item.companyName ? item.companyName : ""  }}
						</view>
						<!-- <view style="width: 70rpx;">
							<u-icon v-if="item.ipicture!='0'" @click="toFj(item)" name="camera" size="70"></u-icon>
						</view> -->
					</view>
					
					<view class="cu-form-group">
						<view class="title">拣货日期：</view>
						<view style="flex: 1;"> {{ item.date|| ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">任务类型：</view>
						<view style="flex: 1;"> 
							<dictLabel style="margin-left: -10px;" :value="item.taskType" dict-type="wh_rd02_bus_type"></dictLabel>
						</view>
					</view>
					<view  class="cu-form-group" >
						<view class="">
						</view>
						<view class="">
							<button  class="cu-btn lines-green shadow-blur" @click="toForm(item)">
								拣货
							</button>
						</view>
					</view>
				
				</view>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>
		<!-- v-if="flag" -->
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				x: 650, //x坐标
				y: 650, //y坐标
				companySelectList:[],
				show: false,
				smshow: false,
				focus: false,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
					type:1,
					// jhStatus:[0,1]
					// orderBy: "a.create_date desc",
				},
				loadStatus: "loadmore",
				triggered: false,
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				jhStatusInName: '',
				type:'',
			};
		},
		onShow() {
			// console.log('页面开启（onLoad）广播监听事件：xwscan')
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
			// this.query.pageNo = 1;
			// this.loadData();
			this.$u.api.ktnw.companyTreeData().then(res => {
				this.companySelectList = res;
			});
			
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onLoad(e) {
			
			this.query.companyCode = this.vuex_company.companyCode || '';
			this.query['company.companyName ']= this.vuex_company.companyName || '';
			
			this.type = e.type
			// console.log(this.type)
			if(this.type == this.vuex_config.jhStatus.ING){
				this.query.jhStatusIn = '1,2'
			}else if(this.type == this.vuex_config.jhStatus.NO){
				// this.query.jhStatusIn = '0,1'
			}
			
			this.loadData()
		},
		mounted() {

			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
			handleFocus() {
				var _that = this;
				_that.focus = false;
				setTimeout(() => {
					_that.focus = true;
				}, 500)
			},
			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					this.confirm()
				}
			},
			confirm() {
				let _this = this;
				if (this.barCode) {
					// this.query.djno = this.barCode
					// this.loadData()
					this.$u.api.ktnw.whListDataJh({
						djno :this.barCode
					}).then((res) => {
						if (res.list.length == 1) {
							_this.sendMp3('cg');
							uni.$off('xwscan', this.BroadcastScanningToObtainData)
							uni.navigateTo({
								url: '/pages/ktnw/qtrkxj/list2?djno=' + res.list[0].djno,
							})
						}else{
							_this.sendMp3('sb');
							_this.$refs.jsError.showError("", "请扫描正常的拣货单号！", "warn");
						}
					});
					
					setTimeout(()=>{
						this.barCode = ''
					},500)
					
					
				} else {
					_this.sendMp3('sb');
					// this.$u.toast("任务单号不能为空!");
					_this.$refs.jsError.showError("", "拣货单号不能为空", "warn");
				}
			},
			search() {
				let _that = this;
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			
		
			toForm(item){
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
				uni.navigateTo({
					url: '/pages/ktnw/qtrkxj/list2?djno=' + item.djno,
				})
			},
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
					type:1
				};
				this.loadData();
				this.show = false
			},
			submit() {
				this.list = [];
				this.query.pageNo = 1;
				this.loadData();
				this.show = false
			},
			async calculateScrollViewHeight() {
				try {
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.ktnw.whListDataJh(this.query).then((res) => {
					if (res.list.length >= 0) {
						// this.xmList = res.list
						if (res.list.length < 20 || res.list.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res.list;
						if (type == "add") {
							for (var i = 0; i < data.length; i++) {
								this.list.push(data[i]);
							}
						} else {
							this.list = data;
						}
					}
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
	};
</script>
<style lang="scss" scoped>
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 260rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>