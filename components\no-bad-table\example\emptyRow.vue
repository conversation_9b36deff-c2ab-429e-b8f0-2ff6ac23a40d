<template>
	<view class="example">
		<view class="title">数据为空</view>
		<v-table :columns="columnsCheckBox" :list="data"></v-table>
	</view>
</template>

<script>
	import vTable from "@/components/no-bad-table/table.vue"
	export default{
		components: {
			vTable
		},
		data(){
			return {
				columnsCheckBox: [{
						title: 'Name',
						key: 'name'
					},
					{
						title: 'Age',
						key: 'age'
					},
					{
						title: 'Address',
						key: 'address'
					}
				],
				data: [],
			}
		}
		
	}
</script>

<style>
</style>
