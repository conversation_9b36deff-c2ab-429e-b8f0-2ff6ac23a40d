<template>
	<view class="b-btns">
		<view v-if="showTraceText"  class="b-btns">
			<view class="b-btn" v-if="isInit && task.procIns.id == ''">
				<u-button class="btn" type="primary" @click="complete" :custom-style="customStyle">{{completeText}}</u-button>
			</view>
			<view class="b-btn" v-if="taskClaim && (task.assignee == null || task.assignee != '')">
				<u-button class="btn" type="success" @click="claim" :custom-style="customStyle">{{claimText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim">
				<u-button class="btn" type="primary" @click="complete" :custom-style="customStyle">{{completeText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim && backText != '' && task.procIns.procDef.form.optionMap.allowBackTask != '0'">
				<u-button class="btn" type="error" @click="back" :custom-style="customStyle">{{backText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim && turnText != '' && task.procIns.procDef.form.optionMap.allowTurnTask != '0'">
				<u-button class="btn" type="warning" @click="turn(false)" :custom-style="customStyle">{{turnText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim && delegateText != '' && task.procIns.procDef.form.optionMap.allowDelegateTask != '0'">
				<u-button class="btn" type="success" @click="turn(true)" :custom-style="customStyle">{{delegateText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim && stopText != '' && (task.procIns.startUserId == vuex_user.userCode || vuex_user.userCode == 'system')">
				<u-button class="btn" type="error" @click="stop" :custom-style="customStyle">{{stopText}}</u-button>
			</view>
			<view class="b-btn" v-if="needClaim && moveText != '' && (vuex_user.userCode == 'system' || task.procIns.procDef.form.optionMap.allowMoveTask == '1')">
				<!-- <u-button class="btn" type="primary" @click="move">{{moveText}}</u-button> -->
			</view>
			<view class="b-btn" v-if="traceText != '' && task.procIns.id != ''">
				<u-button class="btn" type="info" @click="trace" :custom-style="customStyle">{{traceText}}</u-button>
			</view>
			<view class="b-btn" v-if="task.id !='' && rollbackText != '' && task.procIns.procDef.form.optionMap.allowRollbackTask != '0'
					&& task.procIns.id != '' && (task.procIns.endTime == null || task.procIns.endTime == '')
					&& task.id != '' && !(task.endTime == null || task.endTime == '')
					&& task.assignee == vuex_user.userCode">
				<u-button class="btn" type="purple" @click="rollback" :custom-style="customStyle">{{rollbackText}}</u-button>
			</view>
			<view class="b-btn" v-if="navBackText != ''">
				<!-- <u-button class="btn" type="default" @click="navBack">{{navBackText}}</u-button> -->
			</view>
		</view>
		<view v-else  class="b-btns">
			<view class="b-btn" v-if="traceText != '' && task.procIns.id != ''">
				<u-button class="btn" type="info" @click="trace" :custom-style="customStyle">{{traceText}}</u-button>
			</view>
			<view class="b-btn" v-if="task.id !='' && rollbackText != '' && task.procIns.procDef.form.optionMap.allowRollbackTask != '0'
					&& task.procIns.id != '' && (task.procIns.endTime == null || task.procIns.endTime == '')
					&& task.id != '' && !(task.endTime == null || task.endTime == '')
					&& task.assignee == vuex_user.userCode">
				<u-button class="btn" type="purple" @click="rollback" :custom-style="customStyle">{{rollbackText}}</u-button>
			</view>
		</view>
		<u-popup v-model="backModel" mode="bottom" border-radius="30" :closeable="true" :safe-area-inset-bottom="true">
			<view class="form-title">
				<view>任务退回</view>
			</view>
			<u-form class="form" label-position="left">
				<u-form-item label="当前环节" label-width="180">
					{{task.name || '未设置环节名'}}
				</u-form-item>
				<u-form-item v-if="backData.backAutoStop != '1'" label="退回到哪" label-width="180">
					<js-select v-model="backForm.activityId" :items="backData.backActivity"
						item-label="activityName" item-value="activityId" placeholder="请选择环节" height="100%"></js-select>
				</u-form-item>
				<u-form-item label=" " label-width="180" label-position="top">
					<u-input v-model="backForm.comment" type="textarea" placeholder="请输入退回原因" height="150" maxlength="500" />
				</u-form-item>
			</u-form>
			<view class="form-footer">
				<u-button class="btn" type="default" @click="backModel = false" :custom-style="customStyle">取消</u-button>
				<u-button class="btn" type="primary" @click="backTask" :custom-style="customStyle">确定</u-button>
			</view>
		</u-popup>
		<u-popup v-model="turnModel" mode="bottom" border-radius="30" :closeable="true" :safe-area-inset-bottom="true">
			<view class="form-title">
				<view>任务{{turnDelegate?'委托':'转办'}}</view>
			</view>
			<u-form class="form" label-position="left">
				<u-form-item label="当前环节" label-width="180">
					{{task.name || '未设置环节名'}}
				</u-form-item>
				<u-form-item :label="(turnDelegate?'委托':'转办')+'人员'" label-width="180">
					<js-select v-model="turnForm.userCode" :items="userSelectList" placeholder="请选择人员" :tree="true"
						:label-value="turnForm.userName" @label-input="turnForm.userName = $event"></js-select>
				</u-form-item>
				<u-form-item :label="(turnDelegate?'委托':'转办')+'原因'" label-width="180" label-position="top">
					<u-input v-model="backForm.comment" type="textarea" :placeholder="'请输入'+(turnDelegate?'委托':'转办')+'原因'" height="50" maxlength="500" />
				</u-form-item>
			</u-form>
			<view class="form-footer">
				<u-button class="btn" type="default" @click="turnModel = false" :custom-style="customStyle">取消</u-button>
				<u-button class="btn" type="primary" @click="turnTask" :custom-style="customStyle">确定</u-button>
			</view>
		</u-popup>
		<u-popup v-model="stopModel" mode="bottom" border-radius="30" :closeable="true" :safe-area-inset-bottom="true">
			<view class="form-title">
				<view>流程终止</view>
			</view>
			<u-form class="form" label-position="left">
				<u-form-item label="终止原因" label-width="180" label-position="top">
					<u-input v-model="stopForm.deleteReason" type="textarea" placeholder="请输入终止原因" height="50" maxlength="500" />
				</u-form-item>
			</u-form>
			<view class="form-footer">
				<u-button class="btn" type="default" @click="stopModel = false" :custom-style="customStyle">取消</u-button>
				<u-button class="btn" type="primary" @click="stopProcess" :custom-style="customStyle">确定</u-button>
			</view>
		</u-popup>
		<u-popup v-model="completeModel" mode="bottom" border-radius="30" :closeable="true" :safe-area-inset-bottom="true">
			<view class="form-title">
				<view>流程提交</view>
			</view>
			<u-form class="form" label-position="left">
				<u-form-item label=" " label-width="180" label-position="top">
					<u-input v-model="completeForm.bpm.comment" type="textarea" style="text-align: left;" placeholder="请输入提交说明" height="200" maxlength="500" />
				</u-form-item>
			</u-form>
			<view class="form-footer">
				<u-button class="btn" type="default" @click="completeModel = false" :custom-style="customStyle">取消</u-button>
				<u-button class="btn" type="primary" :loading="loading" @click="completeTask" :custom-style="customStyle">确定</u-button>
			</view>
		</u-popup>
	</view>
</template>
<script>
/**
 * 复选框组件
 * @property {Object} value 用于双向绑定选择框的值，返回选择框的 Value
 * @example <js-bpm-button v-model="model.bpm" :biz-key="model.id" form-key="leave" complete-text="提交"></js-bpm-button>
 * @description Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * <AUTHOR>
 * @version 2021-3-11
 */
import bpmUtils from '@/pages/bpm/bpmUtils.js';
export default {
	props: {
		showTraceText: {
			type: Boolean,
			default: true
		},
		value: {
			type: Object,
			default() {
				return {}
			}
		},
		bizKey: {
			type: String,
			default: ''
		},
		formKey: {
			type: String,
			default: ''
		},
		claimText: {
			type: String,
			default: '签 收'
		},
		completeText: {
			type: String,
			default: '提 交'
		},
		backText: {
			type: String,
			default: '退 回'			// 退回到之前办理过的任务环节
		},
		turnText: {
			type: String,
			default: '转 办'			// 将任务转交他人办理，办理完成后继续流转
		},
		delegateText: {
			type: String,
			default: '委 托'			// 任务交由被委托人办理，办理完成后返回委托人
		},
		stopText: {
			type: String,
			default: '终 止'			// 单据作废，结束流程实例
		},
		moveText: {
			type: String,
			default: '自由流' 		// 特事特办，自由流，随意跳转
		},
		traceText: {
			type: String,
			default: '流程图'		// 流程状态，流程图，流程记录
		},
		rollbackText: {
			type: String,
			default: '撤回任务' 		// 下一环节未审批的可撤回重做
		},
		navBackText: {
			type: String,
			default: '返回' 			// 返回页面
		}
	},
	data() {
		return {
			loading:false,
			customStyle:{
				width: '100%',
				maxWidth: '100%'
			},
			options: {
				value: {},
			},
			task: {
				id: '',
				assignee: '',
				procIns: {
					id: '',
					procDef: {
						form: {
							optionMap: {}
						}
					}
				}
			},
			userSelectList: [],
			isInit: false,
			taskClaim: false,
			needClaim: false,
			backModel: false,
			backData: {},
			backForm: {
				id: '',
				activityId: '',
				nextUserCodes: '',
				comment: ''
			},
			turnModel: false,
			turnDelegate: false,
			turnForm: {
				id: '',
				userCode: '',
				userName: '',
				comment: ''
			},
			stopModel: false,
			stopForm: {
				id: '',
				deleteReason: '',
			},
			completeModel: false,
			completeForm:{
				bpm:{
					comment:''
				}
			}
		};
	},
	watch: {
		value(val, oldVal) {
			this.options.value = val;
			this.loadData();
		},
		bizKey(val, oldVal) {
			this.options.bizKey = val;
		}
	},
	created() {
		this.loadData();
	},
	methods: {
		loadData(){
			let taskId = this.options.value.taskId || '',
				procInsId = this.options.value.procInsId || '',
				formKey = this.formKey || '',
				bizKey = this.bizKey || '';
			if (taskId != '' || (formKey != '' && bizKey != '')){
				this.$u.api.bpm.getTask({
					'id': taskId,
					'procIns.formKey': formKey,
					'procIns.bizKey': bizKey
				}).then(res => {
					this.initialize(res);
					this.isInit = true;
				})
			}else{
				this.isInit = true;
			}
		},
		initialize(task = {}) {
			if (!task.id || task.id == ''){
				task.id = this.value.taskId || '';
			}
			if (!task.procIns){ task.procIns = {}; }
			if (!task.procIns.id || task.procIns.id == ''){
				task.procIns.id = this.value.procInsId || '';
			}
			if (task.procIns.id == ''){
				this.taskClaim = false;
				this.needClaim = false;
			}
			else if (task.id != '' && (task.endTime == null || task.endTime == '')){
				if (task.assignee == null || task.assignee == ''){
					this.taskClaim = true;
					this.needClaim = false;
				}else{
					this.taskClaim = false;
					this.needClaim = true;
				}
			}
			this.task = task;
			//console.log('task', this.task)
			this.value.taskId = task.id || '';
			this.value.procInsId = task.procIns.id || '';
			this.value.activityId = task.activityId || '';
			this.$emit('input', Object.assign(this.options.value, this.value));
		},
		complete() {
			if(this.task.procIns.procDef.form.optionMap.bcommitNeedRemarks == '1'){
				this.completeModel = true
			}else{
				this.emit('complete', res => {
					// 有业务功能实现
				});
			}
		},
		completeTask(){
			this.loading = true
			this.options.value.comment = this.completeForm.bpm.comment
			// this.completeModel = false
			this.emit('complete', res => {
				// 有业务功能实现
				setTimeout(()=>{
					this.loading = false
				},8000)
			});
		},
		claim() {
			this.emit('claim', res => {
				this.$u.api.bpm.claim({id: this.task.id}).then(res => {
					this.$u.toast(res.message);
					this.loadData();
				});
			});
		},
		back() {
			this.emit('back', res => {
				this.$u.api.bpm.back({id: this.task.id}).then(res => {
					//console.log('backData', res);
					this.backData = res;
					this.backModel = true;
				});
			});
		},
		backTask() {
			this.backForm.id = this.task.id;
			if (this.backForm.id == ''){
				this.$u.toast('任务ID不能为空');
				return;
			}
			if (this.backForm.activityId == '' && this.backData.backAutoStop != '1'){
				this.$u.toast('请选择退回到哪');
				return;
			}
			if(this.backData.backAutoStop != '1'){
				for (let i in this.backData.backActivity){
					let ba = this.backData.backActivity[i];
					if (ba.activityId == this.backForm.activityId){
						this.backForm.nextUserCodes = ba.assignee;
						break;
					}
				}
			}
			this.backModel = false;
			//console.log('backTask', this.backForm);
			this.$u.api.bpm.backTask(this.backForm).then(res => {
				this.showMessage(res);
			});
		},
		turn(turnDelegate) {
			this.emit(turnDelegate?'delegate':'turn', res => {
				this.turnDelegate = turnDelegate;
				if (this.userSelectList.length == 0){
					this.$u.api.office.treeData({isLoadUser: true}).then(res => {
						this.userSelectList = res;
						this.turnModel = true;
					});
				}else{
					this.turnModel = true;
				}
			});
		},
		turnTask(){
			this.turnForm.id = this.task.id;
			this.turnForm.delegateState = this.turnDelegate;
			if (this.turnForm.id == ''){
				this.$u.toast('任务ID不能为空');
				return;
			}
			if (this.turnForm.userCode == ''){
				this.$u.toast('请选择'+(this.turnDelegate?'委托':'转办')+'人员');
				return;
			}
			this.turnModel = false;
			//console.log('turnTask', this.turnForm);
			this.$u.api.bpm.turnTask(this.turnForm).then(res => {
				this.showMessage(res);
			});
		},
		stop() {
			this.emit('stop', res => {
				this.stopModel = true;
			});
		},
		
		stopProcess() {
			this.stopForm.id = this.task.procIns.id;
			if (this.stopForm.id == ''){
				this.$u.toast('流程ID不能为空');
				return;
			}
			this.stopModel = false;
			//console.log('stopProcess', this.stopForm);
			this.$u.api.bpm.stopProcess(this.stopForm).then(res => {
				this.showMessage(res);
			});
		},
		move() {
			this.emit('move', res => {
				
			});
		},
		trace() {
			this.emit('trace', res => {
				bpmUtils.navTrace(this, 'id=' + this.task.procIns.id);
			});
		},
		rollback() {
			let self = this;
			this.emit('rollback', res => {
				uni.showModal({
					title: '提示',
					content: '如果该任务的下一环节未被处理，则可以尝试撤回该任务的办理。您确认要尝试撤回吗？',
					showCancel: true,
					success: function (res) {
						if(!res.confirm) {
							return;
						}
						self.$u.api.bpm.rollback({id: self.task.id}).then(res => {
							self.showMessage(res);
						});
					}
				});
			});
		},
		// 事件调用，如果 callback(false) 则终止默认事件，举例：
		// name(data, callback) { callback(false); },
		emit(name, callback) {
			//console.log(name, this.task)
			let res = true, data = {};
			this.$emit(name, this, (res2, data2) => {
				if (res2 != undefined){
					res = res2;
				}
				if (data2 != undefined){
					data = data2;
				}
			});
			if (res){
				callback(data);
			}
		},
		showMessage(res) {
			uni.showModal({
				title: '提示',
				content: res.message,
				showCancel: false,
				success: function () {
					if (res.result == 'true'){
						uni.setStorageSync('refreshList', true);
						uni.navigateBack();
					}
				}
			});
		},
		navBack() {
			uni.navigateBack();
		}
	}
}
</script>
<style lang="scss" scoped>
// .b-btns {
// 	display: flex;
// 	flex-wrap: wrap;
// 	justify-content: center;
// 	width: 730rpx;
// 	.b-btn {
// 		display: flex;
// 		flex-wrap: wrap;
// 		width: 100%;
// 	}
// }
// .form-title {
// 	font-size: 34rpx;
// 	padding: 28rpx 30rpx 10rpx;
// }
// .form-footer {
// 	margin: 0;
// 	padding: 0 10rpx;
// 	padding-bottom: 20rpx;
// }
.b-btns {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  // justify-content:space-evenly;
  justify-content: center;
  // width: 730rpx;
  width: 100%;
  .b-btn {
	margin:0 5px ;
    display: flex;
    flex-wrap: wrap;
	// margin: 5px;
    // width: 100%;
  }
}
.form-title {
  font-size: 34rpx;
  padding: 28rpx 30rpx 10rpx;
}
.form-footer {
  margin: 0;
  padding: 0 10rpx;
  padding-bottom: 20rpx;
}
.btns{
	display: flex;
	justify-content:space-evenly !important;
}
.btn{
	width: 100% !important;
	max-width: 100% !important;

}
.u-btn{
	width: 100% !important;
	max-width: 100% !important;
}

</style>
