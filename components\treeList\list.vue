<template>
	<view class="wrap">
	<js-error mode="bottom" ref="jsError"></js-error>
	<view class="search">
		<u-search v-model="query.name" placeholder="请输入单位名称搜索" :show-action="true" @custom="loadList" @search="loadList"></u-search>
	</view>
	<view style="display: flex;overflow: scroll;" :style="[{height:'calc(100vh - 120px)'}]">
		<scroll-view v-if="rangeList.length" scroll-y="true" class="indexes" :scroll-with-animation="true" :enable-back-to-top="true"
			 style="flex: 1;background: #fff;">
			<next-tree :border="true" v-if="rangeList.length" ref="qiantree" :selectParent="false" labelKey="name" valueKey="id" 
				:multiple="false" :treeData="rangeList"  
				@confirm="onconfirm" />
		</scroll-view>
		<view style="flex: 1.5;overflow: scroll;padding: 10rpx;">
			
			<view v-for="(item, index) in list" :key="index" class=" bg-white" style="position: relative;border-radius: 10px;margin-bottom: 10px;border: 1px solid #eee;overflow: hidden;">
			
				<view class="cu-form-group" style="display: flex;justify-content: space-between;">
					
					<view class="">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
					</view>
					<view style="width: 40rpx;">
						<!-- :disabled="item.planWeight?false:true"  -->
						<u-checkbox v-model="item.checked" :size="50" :key="index"
							@change="addData(item)"></u-checkbox>
					</view>
				</view>
				<view class="text padding-left-lg"><text class=""></text>{{item.code}}</view>
				<view class="text padding-left-lg" style="margin: 10rpx 0;"><text class=""></text>{{item.name}}</view>
			</view>
			<u-empty
				v-if="!list.length"
				text='暂无数据!'
				icon="http://cdn.uviewui.com/uview/empty/data.png"
			>
			</u-empty>
		</view>
			

	</view>
	<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
		<button class="cu-btn  lines-red " @click="cancel">关闭</button>
		<button class="cu-btn  bg-confirm  margin-left" @click="submit">确定</button>
	</view>
	</view>
</template>

<script>
	import nextTree from './tree.vue'
	import {
		mapState
	} from 'vuex';
	export default {
		components: {
			nextTree
		},
		data() {
			return {
				list:[],
				addList: [],
				search:'',
				rangeList:[
					{
						id:1,
						name:'运输费用',
						children:[
							// {
							// 	id:1,
							// 	name:'中间箱1',
							// 	checked:true
							// },
							// {
							// 	id:2,
							// 	name:'中间箱2',
								
							// },
							// {
							// 	id:3,
							// 	name:'中间箱3',
								
							// },
						]
					}
					
				],
				num:'',
				show:false,
				infoData:{},
				sonlist:[],
				params:{},
				focus: true,
				numfocus: false,
				menuArrow: true,
				isShowe: false,
				isShowo: false,
				vals: '',
				markCode: '', //装code变量
				query: {
					pageNo: 1,
					pageSize: 999,
				},
			}
		},
		computed: {
			...mapState(['hasLogin', 'userInfo', 'auditlist', 'corpCode']),
		},
		onShow() {

		},
		onLoad(params) {
			let obj = JSON.parse(params.item)
			console.log(obj,'obj===');
			if(obj.id){
				let arrId = obj.id.split(',')
				let arrName = obj.name.split(',')
				arrId.forEach((id,index)=>{
					let d = {
						id:id,
						name:arrName[index]
					}
					this.addList.push(d)
				})
			}
			
			this.$u.api.carVen.treeDataApp().then(res => {
				this.rangeList[0].children = res
				if(this.rangeList[0].children.length){
					this.rangeList[0].children[0].checked=true
					this.query.parentCodes = res[0].id
					this.loadList() 
				}
				this.$forceUpdate()
			})
		},
		methods: {
			addData(item) {
				var addList = this.addList
				for (var i = 0; i < addList.length; i++) {
					if (addList[i].id == item.id) {
						addList.splice(i, 1);
					}
				}
				if (item.checked == false || item.checked == undefined) {
					addList.push(item)
				}
			
			},
			loadList() {
				this.$u.api.carVen.listData(this.query).then(res => {
					this.list = res.map(item=>{
						this.addList.forEach(item2=>{
							if(item.id == item2.id){
								item.checked = true
							}
						})
						return item
					})
				})
			},
			onconfirm(data){
				console.log(data);
				this.query.parentCodes = data.id[0]
				this.loadList() 
			},
			cancel() {
				uni.navigateBack({
					delta: 1,
				})
			},
			submit(){
				if (this.addList.length > 0) {
					console.log(this.addList,'this.addList====');
					let arrId = this.addList.map(item=>{
						return item.id
					})
					let arrName = this.addList.map(item=>{
						return item.name
					})
					
					let obj = {
						id:arrId.join(','),
						name:arrName.join(','),
					}
					uni.$emit('carVenData', obj);
					uni.navigateBack({
						delta: 1,
					})
				} else {
					// this.$u.toast("请选择至少一条数据！");
					this.$refs.jsError.showError('',"请选择至少一条数据！",'error');
					return;
				}
			},
		}
	}
</script>

<style >
	
	
	
	.confirm-btn {
		padding: 0 38upx;
		margin: 0;
		height: 76upx;
		line-height: 76upx;
		font-size: $font-base + 2upx;
		background: $uni-color-primary;
	}

	.page {
		height: 100Vh;
		width: 100vw;
	}

	.page.show {
		overflow: hidden;
	}

	.switch-sex::after {
		content: "\e716";
	}

	.switch-sex::before {
		content: "\e7a9";
	}

	.switch-music::after {
		content: "\e66a";
	}

	.switch-music::before {
		content: "\e6db";
	}

	.indexes {
		font-size: 18px;
	}

	/* 底部栏 */
	.action-section {
		position:fixed;
		left: 75%;
		bottom:30upx;
		z-index: 95;
		display: flex;
		align-items: center;
		background: rgba(255,255,255,.9);
		border-radius: 40upx;
	}
	
	.bg {
		 z-index: 9999;
		 background:rgba(0,0,0,.4);
		 position:fixed;
		 left:0;
		 right:0;
		 top:0;
		 bottom:0;
		 display: flex;
		 justify-content: center;
		 align-items: center;
	}
	
	
	.cu-bar {
		min-height: 70px;
	}
	
	.foot {
		background: #fff;
	}
	
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}
	
	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}
	
	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}
	
	.cu-dialog {
		background: #ffffff;
	}
	
	
	.cu-modal-footer {
		padding: 32rpx 32rpx !important;
		width: 100% ;
		
		
	}
	.cu-btn {
		width: 50%;
		font-size: 16px;
		height: 40px;
	}
	
	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>