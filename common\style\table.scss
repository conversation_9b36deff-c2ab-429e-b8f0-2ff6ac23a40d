$div-table-border-color: #666;
$div-table-border-width: 1upx;
@mixin fixBorderWidth($dir:none) {  
	@if $dir==none{
	
		@media screen and (-webkit-device-pixel-ratio: 1) { 
			border-width: 1px;
		}
		@media screen and (-webkit-device-pixel-ratio: 2) { 
			border-width: .5px;
		}
		@media screen and (-webkit-device-pixel-ratio: 3) { 
			border-width: .5px;
		} 
	}
	@else{
		@media screen and (-webkit-device-pixel-ratio: 1) { 
			border-#{$dir}-width: 1px;
		}
		@media screen and (-webkit-device-pixel-ratio: 2) { 
			border-#{$dir}-width: .5px;
		}
		@media screen and (-webkit-device-pixel-ratio: 3) { 
			border-#{$dir}-width: .5px;
		} 
	}
	
}
.no-bad-table-wrap {
  position: relative;
	overflow: hidden;
//外层容器
  .table_box_big {
    overflow: auto;
    width: 100%;
    position: relative;
    // height: 350px;
  }
  //工具类
  .verticalV{
	 position: absolute;
	 width: 100%;
	 text-align: center;
	 left:50%;
	 top:50%;
	 transform: translateY(-50%);
  }

  .table_box {
    overflow: auto;
    // position: absolute;
  }

  .table_tbody_box {
    // height: 300px;
     // overflow: scroll;
  }

  //没有任何数据
  .empty-data-body-box {
    min-height: 100px;
    line-height: 100px;
    text-align: center;
    color: #666;
    border: 1px solid $div-table-border-color;
	@include fixBorderWidth;
    border-top: 0;

  }

  &.fix-height {

    .table_box_big {
      overflow-x: scroll;
      overflow-y: hidden;
      position: relative;

      // height: 350px;
    }

    .table_box {
      overflow: hidden;
      position: absolute;
    }

    .table_tbody_box {
      // height: 300px;
      overflow: scroll;
      border-bottom: 1px solid $div-table-border-color;
	  @include fixBorderWidth(bottom);
    }

    //固定高低里面的内容表格底部不要边框
    .div-table-body {
      border-top: 0;
	  border-bottom:0;
	  //固定高度，数据为空
	  &.empty-data-body-box{
		  border-bottom: $div-table-border-width solid $div-table-border-color;
		   @include fixBorderWidth(bottom);
	  }
    }
  }

  //头部表格
  .th,
  .thead .tr,
  .fixed-thead-tr {
    .td {
      background-color: #e0e0ea;

      .td_wrap {
        background-color: #e0e0ea;
      }

    }
  }

  //固定右边一列
  .fixed-right {
    position: absolute;
    top: 0upx;
    right: 0px;
    z-index: 100;
    // border-right: $div-table-border-width solid $div-table-border-color;
    // border-left: $div-table-border-width solid $div-table-border-color;
    box-shadow: -2px 0 5px rgba(0, 0, 0, .5);
  }

  //固定左边一列
  .fixed-left {
    position: absolute;
    top: 0upx;
    left: -1px;
    z-index: 100;
    border-right: $div-table-border-width solid $div-table-border-color;
    border-left: $div-table-border-width solid $div-table-border-color;
	 @include fixBorderWidth(right);
	 @include fixBorderWidth(left);
    box-shadow: 2px 0 5px rgba(0, 0, 0, .5);
  }

  .tr,
  .th {
    display: table-row;

    &+.tr,
    &+.th {

      .td,
      .th {
        border-top: $div-table-border-width solid $div-table-border-color;
		@include fixBorderWidth(top);
        word-break: break-word;
      }
    }
  }
  //外层容器控制td左右居中
  &.td-center{
	  .td {
		  .td_wrap{
			  text-align: center;
		  }
	  }
  }
  .td {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    box-sizing: border-box;
    z-index: 1;
    position: relative;
    overflow: hidden;

    .td_wrap {
      position: relative;
      padding: 10upx;
      box-sizing: border-box;
      overflow: hidden;
      line-height: 20px;
      font-size: 12px;
      background: #fff;
	  text-align: left;
    }

    &.colspan {
      z-index: 10;
      position: absolute;
      .td_wrap {
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        position: absolute;
      }
    }

    &.rowspan {
      position: absolute;
      z-index: 10;
    }

    &.empty-cells-for-celspan {
      border-left: none !important;

      .td_wrap {
        opacity: 0;
      }
    }

    &.empty-cells-for-rowspan {
      border-top: none !important;

      .td_wrap {
        opacity: 0;
      }
    }

    &.noPadding {
      padding: 0;
    }

    &+.td {
      .td_wrap {
        border-left: $div-table-border-width solid $div-table-border-color;
		@include fixBorderWidth(left);
      }
    }
  }

  .th .td {
    font-weight: bold;
  }
      //单选样式========》选中
  .selected {
    .td {
      background-color: #d3e3ef;
  
      .td_wrap {
        background-color: #d3e3ef;
      }
    }
  }
  
  //单选样式===》禁用
  .disabled {
    .td {
      background-color: #f8f8f9;
      opacity: .6;
    }
  }

  .div-table {
    display: table;
    border: $div-table-border-width solid $div-table-border-color;
	@include fixBorderWidth;
    box-sizing: border-box;
    table-layout: fixed;
    position: relative;

    &.div-table-body {
      border-top: 0;
    }
    .tbody {
      display: table-row-group;
    }

    .thead {
      display: table-header-group;

//       .tr,
//       .th {
// 
//         .td,
//         .th {
//           width: 120upx;
//           height: 75px;
//         }
//       }
    }

    .colgroup {
      display: table-column-group;
    }

    .col {
      display: table-column;
    }

    .caption {
      display: table-caption;
    }

  }
}