<script>
	/**
	 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
	 */
	export default {
		watch: {
			vuex_cartList(nVal, oVal) {
				let that = this
				// uni.setTabBarBadge({
				// 	index: 2,
				// 	text: that.vuex_cartList + ''
				// });
				// if (!that.vuex_cartList) {
				// 	uni.removeTabBarBadge({
				// 		index: 2
				// 	});
				// }
			}
		},
		onLaunch() {
			// 国际化，设置当前语言
			if (this.vuex_locale) {
				this.$i18n.locale = this.vuex_locale;
				this.$u.api.lang({
					lang: this.vuex_locale
				});
			}
			// 设置底部导航栏角标
			// uni.setTabBarBadge({
			// 	index: 2,
			// 	text: this.vuex_cartList + ''
			// });
			// if (!this.vuex_cartList) {
			// 	uni.removeTabBarBadge({
			// 		index: 2
			// 	});
			// }
		}
	}
</script>
<style lang="scss">
	
	@import "uview-ui/index.scss";
	@import "pages/common/jeesite.scss";
	@import "colorui/main.css";
	@import "colorui/icon.css";
</style>
<style lang="scss">
	uni-modal {
	  z-index: 999999 !important;
	}
</style>
